{"version": 3, "sources": ["../../src/server/require-hook.ts"], "names": ["path", "require", "mod", "originalRequire", "prototype", "resolveFilename", "_resolveFilename", "resolve", "process", "env", "NEXT_MINIMAL", "__non_webpack_require__", "hookPropertyMap", "Map", "defaultOverrides", "dirname", "toResolveMap", "map", "Object", "entries", "key", "value", "addHookAliases", "aliases", "set", "originalResolveFilename", "requestMap", "request", "parent", "is<PERSON><PERSON>", "options", "hookResolved", "get", "call", "bind", "endsWith", "basename"], "mappings": "AAAA,uHAAuH;AACvH,0FAA0F;AAC1F,kGAAkG;AAElG,oDAAoD;AACpD,MAAMA,OAAOC,QAAQ;AACrB,MAAMC,MAAMD,QAAQ;AACpB,MAAME,kBAAkBD,IAAIE,SAAS,CAACH,OAAO;AAC7C,MAAMI,kBAAkBH,IAAII,gBAAgB;AAE5C,IAAIC,UAAkCC,QAAQC,GAAG,CAACC,YAAY,GAE1DC,wBAAwBJ,OAAO,GAC/BN,QAAQM,OAAO;AAEnB,OAAO,MAAMK,kBAAkB,IAAIC,MAAK;AAExC,OAAO,MAAMC,mBAAmB;IAC9B,cAAcd,KAAKe,OAAO,CAACR,QAAQ;IACnC,oBAAoBA,QAAQ;AAC9B,EAAC;AAED,MAAMS,eAAe,CAACC,MACpBC,OAAOC,OAAO,CAACF,KAAKA,GAAG,CAAC,CAAC,CAACG,KAAKC,MAAM,GAAK;YAACD;YAAKb,QAAQc;SAAO;AAEjE,OAAO,SAASC,eAAeC,UAA8B,EAAE;IAC7D,KAAK,MAAM,CAACH,KAAKC,MAAM,IAAIE,QAAS;QAClCX,gBAAgBY,GAAG,CAACJ,KAAKC;IAC3B;AACF;AAEAC,eAAeN,aAAaF;AAE5BZ,IAAII,gBAAgB,GAAG,CAAA,SACrBmB,uBAKW,EACXC,UAA+B,EAC/BC,OAAe,EACfC,MAAc,EACdC,MAAe,EACfC,OAAY;IAEZ,MAAMC,eAAeL,WAAWM,GAAG,CAACL;IACpC,IAAII,cAAcJ,UAAUI;IAE5B,OAAON,wBAAwBQ,IAAI,CAAC/B,KAAKyB,SAASC,QAAQC,QAAQC;AAElE,8FAA8F;AAChG,CAAA,EAAEI,IAAI,CAAC,MAAM7B,iBAAiBO;AAE9B,2FAA2F;AAC3F,0FAA0F;AAC1F,iGAAiG;AACjGV,IAAIE,SAAS,CAACH,OAAO,GAAG,SAAU0B,OAAe;IAC/C,IAAIA,QAAQQ,QAAQ,CAAC,oBAAoB;QACvC,OAAOhC,gBAAgB8B,IAAI,CACzB,IAAI,EACJ,CAAC,8DAA8D,EAAEjC,KAAKoC,QAAQ,CAC5ET,SACA,mBACA,CAAC;IAEP;IAEA,OAAOxB,gBAAgB8B,IAAI,CAAC,IAAI,EAAEN;AACpC"}