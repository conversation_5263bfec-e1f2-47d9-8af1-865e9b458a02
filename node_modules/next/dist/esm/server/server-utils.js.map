{"version": 3, "sources": ["../../src/server/server-utils.ts"], "names": ["format", "formatUrl", "parse", "parseUrl", "normalizeLocalePath", "getPathMatch", "getNamedRouteRegex", "getRouteMatcher", "matchHas", "prepareDestination", "removeTrailingSlash", "normalizeRscURL", "NEXT_QUERY_PARAM_PREFIX", "normalizeVercelUrl", "req", "trustQuery", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pageIsDynamic", "defaultRouteRegex", "_parsedUrl", "url", "search", "key", "Object", "keys", "query", "startsWith", "groups", "includes", "interpolateDynamicPath", "pathname", "params", "param", "optional", "repeat", "builtParam", "paramIdx", "indexOf", "paramValue", "value", "Array", "isArray", "map", "v", "encodeURIComponent", "join", "slice", "length", "getUtils", "page", "i18n", "basePath", "rewrites", "trailingSlash", "caseSensitive", "dynamicRouteMatcher", "defaultRouteMatches", "handleRewrites", "parsedUrl", "rewriteParams", "fsPathname", "matchesPage", "fsPathnameNoSlash", "checkRewrite", "rewrite", "matcher", "source", "removeUnnamedP<PERSON>ms", "strict", "sensitive", "has", "missing", "hasParams", "assign", "parsedDestination", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "appendParamsToQuery", "destination", "protocol", "replace", "RegExp", "destLocalePathResult", "locales", "nextInternalLocale", "detectedLocale", "dynamicParams", "beforeFiles", "finished", "afterFiles", "fallback", "getParamsFromRouteMatches", "renderOpts", "routeKeys", "re", "exec", "str", "obj", "fromEntries", "URLSearchParams", "matchesHasLocale", "normalizedKey", "substring", "routeKeyNames", "filterLocaleItem", "val", "isCatchAll", "_val", "some", "item", "toLowerCase", "locale", "splice", "every", "name", "reduce", "prev", "keyName", "paramName", "pos", "parseInt", "headers", "normalizeDynamicRouteParams", "ignoreOptional", "hasValidParams", "defaultValue", "isOptional", "isDefaultValue", "defaultVal", "undefined", "split"], "mappings": "AAOA,SAASA,UAAUC,SAAS,EAAEC,SAASC,QAAQ,QAAQ,MAAK;AAC5D,SAASC,mBAAmB,QAAQ,2CAA0C;AAC9E,SAASC,YAAY,QAAQ,wCAAuC;AACpE,SAASC,kBAAkB,QAAQ,yCAAwC;AAC3E,SAASC,eAAe,QAAQ,2CAA0C;AAC1E,SACEC,QAAQ,EACRC,kBAAkB,QACb,iDAAgD;AACvD,SAASC,mBAAmB,QAAQ,mDAAkD;AACtF,SAASC,eAAe,QAAQ,uCAAsC;AACtE,SAASC,uBAAuB,QAAQ,mBAAkB;AAE1D,OAAO,SAASC,mBACdC,GAAoB,EACpBC,UAAmB,EACnBC,SAAoB,EACpBC,aAAuB,EACvBC,iBAAqE;IAErE,mEAAmE;IACnE,gDAAgD;IAChD,IAAID,iBAAiBF,cAAcG,mBAAmB;QACpD,MAAMC,aAAahB,SAASW,IAAIM,GAAG,EAAG;QACtC,OAAO,AAACD,WAAmBE,MAAM;QAEjC,KAAK,MAAMC,OAAOC,OAAOC,IAAI,CAACL,WAAWM,KAAK,EAAG;YAC/C,IACE,AAACH,QAAQV,2BACPU,IAAII,UAAU,CAACd,4BACjB,AAACI,CAAAA,aAAaO,OAAOC,IAAI,CAACN,kBAAkBS,MAAM,CAAA,EAAGC,QAAQ,CAACN,MAC9D;gBACA,OAAOH,WAAWM,KAAK,CAACH,IAAI;YAC9B;QACF;QACAR,IAAIM,GAAG,GAAGnB,UAAUkB;IACtB;AACF;AAEA,OAAO,SAASU,uBACdC,QAAgB,EAChBC,MAAsB,EACtBb,iBAAqE;IAErE,IAAI,CAACA,mBAAmB,OAAOY;IAE/B,KAAK,MAAME,SAAST,OAAOC,IAAI,CAACN,kBAAkBS,MAAM,EAAG;QACzD,MAAM,EAAEM,QAAQ,EAAEC,MAAM,EAAE,GAAGhB,kBAAkBS,MAAM,CAACK,MAAM;QAC5D,IAAIG,aAAa,CAAC,CAAC,EAAED,SAAS,QAAQ,GAAG,EAAEF,MAAM,CAAC,CAAC;QAEnD,IAAIC,UAAU;YACZE,aAAa,CAAC,CAAC,EAAEA,WAAW,CAAC,CAAC;QAChC;QAEA,MAAMC,WAAWN,SAAUO,OAAO,CAACF;QAEnC,IAAIC,WAAW,CAAC,GAAG;YACjB,IAAIE;YACJ,MAAMC,QAAQR,MAAM,CAACC,MAAM;YAE3B,IAAIQ,MAAMC,OAAO,CAACF,QAAQ;gBACxBD,aAAaC,MAAMG,GAAG,CAAC,CAACC,IAAMA,KAAKC,mBAAmBD,IAAIE,IAAI,CAAC;YACjE,OAAO,IAAIN,OAAO;gBAChBD,aAAaM,mBAAmBL;YAClC,OAAO;gBACLD,aAAa;YACf;YAEAR,WACEA,SAASgB,KAAK,CAAC,GAAGV,YAClBE,aACAR,SAASgB,KAAK,CAACV,WAAWD,WAAWY,MAAM;QAC/C;IACF;IAEA,OAAOjB;AACT;AAEA,OAAO,SAASkB,SAAS,EACvBC,IAAI,EACJC,IAAI,EACJC,QAAQ,EACRC,QAAQ,EACRnC,aAAa,EACboC,aAAa,EACbC,aAAa,EAad;IACC,IAAIpC;IACJ,IAAIqC;IACJ,IAAIC;IAEJ,IAAIvC,eAAe;QACjBC,oBAAoBZ,mBAAmB2C,MAAM;QAC7CM,sBAAsBhD,gBAAgBW;QACtCsC,sBAAsBD,oBAAoBN;IAC5C;IAEA,SAASQ,eAAe3C,GAAoB,EAAE4C,SAA6B;QACzE,MAAMC,gBAAgB,CAAC;QACvB,IAAIC,aAAaF,UAAU5B,QAAQ;QAEnC,MAAM+B,cAAc;YAClB,MAAMC,oBAAoBpD,oBAAoBkD,cAAc;YAC5D,OACEE,sBAAsBpD,oBAAoBuC,UAC1CM,uCAAAA,oBAAsBO;QAE1B;QAEA,MAAMC,eAAe,CAACC;YACpB,MAAMC,UAAU5D,aACd2D,QAAQE,MAAM,GAAIb,CAAAA,gBAAgB,SAAS,EAAC,GAC5C;gBACEc,qBAAqB;gBACrBC,QAAQ;gBACRC,WAAW,CAAC,CAACf;YACf;YAEF,IAAIvB,SAASkC,QAAQP,UAAU5B,QAAQ;YAEvC,IAAI,AAACkC,CAAAA,QAAQM,GAAG,IAAIN,QAAQO,OAAO,AAAD,KAAMxC,QAAQ;gBAC9C,MAAMyC,YAAYhE,SAChBM,KACA4C,UAAUjC,KAAK,EACfuC,QAAQM,GAAG,EACXN,QAAQO,OAAO;gBAGjB,IAAIC,WAAW;oBACbjD,OAAOkD,MAAM,CAAC1C,QAAQyC;gBACxB,OAAO;oBACLzC,SAAS;gBACX;YACF;YAEA,IAAIA,QAAQ;gBACV,MAAM,EAAE2C,iBAAiB,EAAEC,SAAS,EAAE,GAAGlE,mBAAmB;oBAC1DmE,qBAAqB;oBACrBC,aAAab,QAAQa,WAAW;oBAChC9C,QAAQA;oBACRN,OAAOiC,UAAUjC,KAAK;gBACxB;gBAEA,6DAA6D;gBAC7D,IAAIiD,kBAAkBI,QAAQ,EAAE;oBAC9B,OAAO;gBACT;gBAEAvD,OAAOkD,MAAM,CAACd,eAAegB,WAAW5C;gBACxCR,OAAOkD,MAAM,CAACf,UAAUjC,KAAK,EAAEiD,kBAAkBjD,KAAK;gBACtD,OAAO,AAACiD,kBAA0BjD,KAAK;gBAEvCF,OAAOkD,MAAM,CAACf,WAAWgB;gBAEzBd,aAAaF,UAAU5B,QAAQ;gBAE/B,IAAIqB,UAAU;oBACZS,aACEA,WAAYmB,OAAO,CAAC,IAAIC,OAAO,CAAC,CAAC,EAAE7B,SAAS,CAAC,GAAG,OAAO;gBAC3D;gBAEA,IAAID,MAAM;oBACR,MAAM+B,uBAAuB7E,oBAC3BwD,YACAV,KAAKgC,OAAO;oBAEdtB,aAAaqB,qBAAqBnD,QAAQ;oBAC1C4B,UAAUjC,KAAK,CAAC0D,kBAAkB,GAChCF,qBAAqBG,cAAc,IAAIrD,OAAOoD,kBAAkB;gBACpE;gBAEA,IAAIvB,eAAeX,MAAM;oBACvB,OAAO;gBACT;gBAEA,IAAIhC,iBAAiBsC,qBAAqB;oBACxC,MAAM8B,gBAAgB9B,oBAAoBK;oBAC1C,IAAIyB,eAAe;wBACjB3B,UAAUjC,KAAK,GAAG;4BAChB,GAAGiC,UAAUjC,KAAK;4BAClB,GAAG4D,aAAa;wBAClB;wBACA,OAAO;oBACT;gBACF;YACF;YACA,OAAO;QACT;QAEA,KAAK,MAAMrB,WAAWZ,SAASkC,WAAW,IAAI,EAAE,CAAE;YAChDvB,aAAaC;QACf;QAEA,IAAIJ,eAAeX,MAAM;YACvB,IAAIsC,WAAW;YAEf,KAAK,MAAMvB,WAAWZ,SAASoC,UAAU,IAAI,EAAE,CAAE;gBAC/CD,WAAWxB,aAAaC;gBACxB,IAAIuB,UAAU;YAChB;YAEA,IAAI,CAACA,YAAY,CAAC1B,eAAe;gBAC/B,KAAK,MAAMG,WAAWZ,SAASqC,QAAQ,IAAI,EAAE,CAAE;oBAC7CF,WAAWxB,aAAaC;oBACxB,IAAIuB,UAAU;gBAChB;YACF;QACF;QACA,OAAO5B;IACT;IAEA,SAAS+B,0BACP5E,GAAoB,EACpB6E,UAAgB,EAChBP,cAAuB;QAEvB,OAAO7E,gBACL,AAAC;YACC,MAAM,EAAEoB,MAAM,EAAEiE,SAAS,EAAE,GAAG1E;YAE9B,OAAO;gBACL2E,IAAI;oBACF,qDAAqD;oBACrDC,MAAM,CAACC;wBACL,MAAMC,MAAMzE,OAAO0E,WAAW,CAAC,IAAIC,gBAAgBH;wBACnD,MAAMI,mBACJjD,QAAQkC,kBAAkBY,GAAG,CAAC,IAAI,KAAKZ;wBAEzC,KAAK,MAAM9D,OAAOC,OAAOC,IAAI,CAACwE,KAAM;4BAClC,MAAMzD,QAAQyD,GAAG,CAAC1E,IAAI;4BAEtB,IACEA,QAAQV,2BACRU,IAAII,UAAU,CAACd,0BACf;gCACA,MAAMwF,gBAAgB9E,IAAI+E,SAAS,CACjCzF,wBAAwBmC,MAAM;gCAEhCiD,GAAG,CAACI,cAAc,GAAG7D;gCACrB,OAAOyD,GAAG,CAAC1E,IAAI;4BACjB;wBACF;wBAEA,mCAAmC;wBACnC,MAAMgF,gBAAgB/E,OAAOC,IAAI,CAACoE,aAAa,CAAC;wBAChD,MAAMW,mBAAmB,CAACC;4BACxB,IAAItD,MAAM;gCACR,gDAAgD;gCAChD,4CAA4C;gCAC5C,WAAW;gCACX,MAAMuD,aAAajE,MAAMC,OAAO,CAAC+D;gCACjC,MAAME,OAAOD,aAAaD,GAAG,CAAC,EAAE,GAAGA;gCAEnC,IACE,OAAOE,SAAS,YAChBxD,KAAKgC,OAAO,CAACyB,IAAI,CAAC,CAACC;oCACjB,IAAIA,KAAKC,WAAW,OAAOH,KAAKG,WAAW,IAAI;wCAC7CzB,iBAAiBwB;wCACjBjB,WAAWmB,MAAM,GAAG1B;wCACpB,OAAO;oCACT;oCACA,OAAO;gCACT,IACA;oCACA,wCAAwC;oCACxC,IAAIqB,YAAY;wCACZD,IAAiBO,MAAM,CAAC,GAAG;oCAC/B;oCAEA,sCAAsC;oCACtC,qBAAqB;oCACrB,OAAON,aAAaD,IAAIzD,MAAM,KAAK,IAAI;gCACzC;4BACF;4BACA,OAAO;wBACT;wBAEA,IAAIuD,cAAcU,KAAK,CAAC,CAACC,OAASjB,GAAG,CAACiB,KAAK,GAAG;4BAC5C,OAAOX,cAAcY,MAAM,CAAC,CAACC,MAAMC;gCACjC,MAAMC,YAAYzB,6BAAAA,SAAW,CAACwB,QAAQ;gCAEtC,IAAIC,aAAa,CAACd,iBAAiBP,GAAG,CAACoB,QAAQ,GAAG;oCAChDD,IAAI,CAACxF,MAAM,CAAC0F,UAAU,CAACC,GAAG,CAAC,GAAGtB,GAAG,CAACoB,QAAQ;gCAC5C;gCACA,OAAOD;4BACT,GAAG,CAAC;wBACN;wBAEA,OAAO5F,OAAOC,IAAI,CAACwE,KAAKkB,MAAM,CAAC,CAACC,MAAM7F;4BACpC,IAAI,CAACiF,iBAAiBP,GAAG,CAAC1E,IAAI,GAAG;gCAC/B,IAAI8E,gBAAgB9E;gCAEpB,IAAI6E,kBAAkB;oCACpBC,gBAAgBmB,SAASjG,KAAK,MAAM,IAAI;gCAC1C;gCACA,OAAOC,OAAOkD,MAAM,CAAC0C,MAAM;oCACzB,CAACf,cAAc,EAAEJ,GAAG,CAAC1E,IAAI;gCAC3B;4BACF;4BACA,OAAO6F;wBACT,GAAG,CAAC;oBACN;gBACF;gBACAxF;YACF;QACF,KACAb,IAAI0G,OAAO,CAAC,sBAAsB;IACtC;IAEA,SAASC,4BACP1F,MAAsB,EACtB2F,cAAwB;QAExB,IAAIC,iBAAiB;QACrB,IAAI,CAACzG,mBAAmB,OAAO;YAAEa;YAAQ4F,gBAAgB;QAAM;QAE/D5F,SAASR,OAAOC,IAAI,CAACN,kBAAkBS,MAAM,EAAEuF,MAAM,CAAC,CAACC,MAAM7F;YAC3D,IAAIiB,QAAuCR,MAAM,CAACT,IAAI;YAEtD,IAAI,OAAOiB,UAAU,UAAU;gBAC7BA,QAAQ5B,gBAAgB4B;YAC1B;YACA,IAAIC,MAAMC,OAAO,CAACF,QAAQ;gBACxBA,QAAQA,MAAMG,GAAG,CAAC,CAAC8D;oBACjB,IAAI,OAAOA,QAAQ,UAAU;wBAC3BA,MAAM7F,gBAAgB6F;oBACxB;oBACA,OAAOA;gBACT;YACF;YAEA,uDAAuD;YACvD,0DAA0D;YAC1D,sCAAsC;YACtC,MAAMoB,eAAepE,mBAAoB,CAAClC,IAAI;YAC9C,MAAMuG,aAAa3G,kBAAmBS,MAAM,CAACL,IAAI,CAACW,QAAQ;YAE1D,MAAM6F,iBAAiBtF,MAAMC,OAAO,CAACmF,gBACjCA,aAAajB,IAAI,CAAC,CAACoB;gBACjB,OAAOvF,MAAMC,OAAO,CAACF,SACjBA,MAAMoE,IAAI,CAAC,CAACH,MAAQA,IAAI5E,QAAQ,CAACmG,eACjCxF,yBAAAA,MAAOX,QAAQ,CAACmG;YACtB,KACAxF,yBAAAA,MAAOX,QAAQ,CAACgG;YAEpB,IACEE,kBACC,OAAOvF,UAAU,eAAe,CAAEsF,CAAAA,cAAcH,cAAa,GAC9D;gBACAC,iBAAiB;YACnB;YAEA,gEAAgE;YAChE,oBAAoB;YACpB,IACEE,cACC,CAAA,CAACtF,SACCC,MAAMC,OAAO,CAACF,UACbA,MAAMQ,MAAM,KAAK,KACjB,6CAA6C;YAC7C,+CAA+C;YAC9CR,CAAAA,KAAK,CAAC,EAAE,KAAK,WAAWA,KAAK,CAAC,EAAE,KAAK,CAAC,KAAK,EAAEjB,IAAI,EAAE,CAAC,AAAD,CAAE,GAC1D;gBACAiB,QAAQyF;gBACR,OAAOjG,MAAM,CAACT,IAAI;YACpB;YAEA,+DAA+D;YAC/D,6CAA6C;YAC7C,IACEiB,SACA,OAAOA,UAAU,YACjBrB,kBAAmBS,MAAM,CAACL,IAAI,CAACY,MAAM,EACrC;gBACAK,QAAQA,MAAM0F,KAAK,CAAC;YACtB;YAEA,IAAI1F,OAAO;gBACT4E,IAAI,CAAC7F,IAAI,GAAGiB;YACd;YACA,OAAO4E;QACT,GAAG,CAAC;QAEJ,OAAO;YACLpF;YACA4F;QACF;IACF;IAEA,OAAO;QACLlE;QACAvC;QACAqC;QACAC;QACAkC;QACA+B;QACA5G,oBAAoB,CAClBC,KACAC,YACAC,YAEAH,mBACEC,KACAC,YACAC,WACAC,eACAC;QAEJW,wBAAwB,CACtBC,UACAC,SACGF,uBAAuBC,UAAUC,QAAQb;IAChD;AACF"}