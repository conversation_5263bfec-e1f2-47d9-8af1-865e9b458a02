{"version": 3, "sources": ["../../src/server/render-result.ts"], "names": ["chainStreams", "streamFromString", "streamToString", "isAbortError", "pipeToNodeResponse", "RenderResult", "fromStatic", "value", "constructor", "response", "contentType", "waitUntil", "metadata", "extendMetadata", "Object", "assign", "isNull", "isDynamic", "toUnchunkedString", "stream", "Error", "readable", "Array", "isArray", "chain", "responses", "push", "pipeTo", "writable", "err", "res"], "mappings": "AAIA,SACEA,YAAY,EACZC,gBAAgB,EAChBC,cAAc,QACT,yCAAwC;AAC/C,SAASC,YAAY,EAAEC,kBAAkB,QAAQ,kBAAiB;AAmClE,eAAe,MAAMC;IAqBnB;;;;;GAKC,GACD,OAAcC,WAAWC,KAAa,EAAgB;QACpD,OAAO,IAAIF,aAAaE;IAC1B;IAIAC,YACEC,QAA8B,EAC9B,EACEC,WAAW,EACXC,SAAS,EACT,GAAGC,UAGmB,GAAG,CAAC,CAAC,CAC7B;QACA,IAAI,CAACH,QAAQ,GAAGA;QAChB,IAAI,CAACC,WAAW,GAAGA;QACnB,IAAI,CAACE,QAAQ,GAAGA;QAChB,IAAI,CAACD,SAAS,GAAGA;IACnB;IAEOE,eAAeD,QAA8B,EAAE;QACpDE,OAAOC,MAAM,CAAC,IAAI,CAACH,QAAQ,EAAEA;IAC/B;IAEA;;;GAGC,GACD,IAAWI,SAAkB;QAC3B,OAAO,IAAI,CAACP,QAAQ,KAAK;IAC3B;IAEA;;;GAGC,GACD,IAAWQ,YAAqB;QAC9B,OAAO,OAAO,IAAI,CAACR,QAAQ,KAAK;IAClC;IAWOS,kBAAkBC,SAAS,KAAK,EAA4B;QACjE,IAAI,IAAI,CAACV,QAAQ,KAAK,MAAM;YAC1B,MAAM,IAAIW,MAAM;QAClB;QAEA,IAAI,OAAO,IAAI,CAACX,QAAQ,KAAK,UAAU;YACrC,IAAI,CAACU,QAAQ;gBACX,MAAM,IAAIC,MACR;YAEJ;YAEA,OAAOlB,eAAe,IAAI,CAACmB,QAAQ;QACrC;QAEA,OAAO,IAAI,CAACZ,QAAQ;IACtB;IAEA;;;GAGC,GACD,IAAYY,WAAuC;QACjD,IAAI,IAAI,CAACZ,QAAQ,KAAK,MAAM;YAC1B,MAAM,IAAIW,MAAM;QAClB;QACA,IAAI,OAAO,IAAI,CAACX,QAAQ,KAAK,UAAU;YACrC,MAAM,IAAIW,MAAM;QAClB;QAEA,oEAAoE;QACpE,IAAIE,MAAMC,OAAO,CAAC,IAAI,CAACd,QAAQ,GAAG;YAChC,OAAOT,gBAAgB,IAAI,CAACS,QAAQ;QACtC;QAEA,OAAO,IAAI,CAACA,QAAQ;IACtB;IAEA;;;;;;;GAOC,GACD,AAAOe,MAAMH,QAAoC,EAAE;QACjD,IAAI,IAAI,CAACZ,QAAQ,KAAK,MAAM;YAC1B,MAAM,IAAIW,MAAM;QAClB;QAEA,mEAAmE;QACnE,IAAIK;QACJ,IAAI,OAAO,IAAI,CAAChB,QAAQ,KAAK,UAAU;YACrCgB,YAAY;gBAACxB,iBAAiB,IAAI,CAACQ,QAAQ;aAAE;QAC/C,OAAO,IAAIa,MAAMC,OAAO,CAAC,IAAI,CAACd,QAAQ,GAAG;YACvCgB,YAAY,IAAI,CAAChB,QAAQ;QAC3B,OAAO;YACLgB,YAAY;gBAAC,IAAI,CAAChB,QAAQ;aAAC;QAC7B;QAEA,mCAAmC;QACnCgB,UAAUC,IAAI,CAACL;QAEf,uBAAuB;QACvB,IAAI,CAACZ,QAAQ,GAAGgB;IAClB;IAEA;;;;;GAKC,GACD,MAAaE,OAAOC,QAAoC,EAAiB;QACvE,IAAI;YACF,MAAM,IAAI,CAACP,QAAQ,CAACM,MAAM,CAACC;QAC7B,EAAE,OAAOC,KAAK;YACZ,yDAAyD;YACzD,IAAI,CAAC1B,aAAa0B,MAAM;gBACtB,MAAMA;YACR;QACF,SAAU;YACR,IAAI,IAAI,CAAClB,SAAS,EAAE;gBAClB,MAAM,IAAI,CAACA,SAAS;YACtB;QACF;IACF;IAEA;;;;;GAKC,GACD,MAAaP,mBAAmB0B,GAAmB,EAAE;QACnD,IAAI;YACF,MAAM1B,mBAAmB,IAAI,CAACiB,QAAQ,EAAES;QAC1C,SAAU;YACR,IAAI,IAAI,CAACnB,SAAS,EAAE;gBAClB,MAAM,IAAI,CAACA,SAAS;YACtB;QACF;IACF;AACF"}