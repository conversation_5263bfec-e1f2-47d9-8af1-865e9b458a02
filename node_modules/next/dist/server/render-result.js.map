{"version": 3, "sources": ["../../src/server/render-result.ts"], "names": ["RenderResult", "fromStatic", "value", "constructor", "response", "contentType", "waitUntil", "metadata", "extendMetadata", "Object", "assign", "isNull", "isDynamic", "toUnchunkedString", "stream", "Error", "streamToString", "readable", "Array", "isArray", "chainStreams", "chain", "responses", "streamFromString", "push", "pipeTo", "writable", "err", "isAbortError", "pipeToNodeResponse", "res"], "mappings": ";;;;+BA4CA;;;eAAqBA;;;sCApCd;8BAC0C;AAmClC,MAAMA;IAqBnB;;;;;GAKC,GACD,OAAcC,WAAWC,KAAa,EAAgB;QACpD,OAAO,IAAIF,aAAaE;IAC1B;IAIAC,YACEC,QAA8B,EAC9B,EACEC,WAAW,EACXC,SAAS,EACT,GAAGC,UAGmB,GAAG,CAAC,CAAC,CAC7B;QACA,IAAI,CAACH,QAAQ,GAAGA;QAChB,IAAI,CAACC,WAAW,GAAGA;QACnB,IAAI,CAACE,QAAQ,GAAGA;QAChB,IAAI,CAACD,SAAS,GAAGA;IACnB;IAEOE,eAAeD,QAA8B,EAAE;QACpDE,OAAOC,MAAM,CAAC,IAAI,CAACH,QAAQ,EAAEA;IAC/B;IAEA;;;GAGC,GACD,IAAWI,SAAkB;QAC3B,OAAO,IAAI,CAACP,QAAQ,KAAK;IAC3B;IAEA;;;GAGC,GACD,IAAWQ,YAAqB;QAC9B,OAAO,OAAO,IAAI,CAACR,QAAQ,KAAK;IAClC;IAWOS,kBAAkBC,SAAS,KAAK,EAA4B;QACjE,IAAI,IAAI,CAACV,QAAQ,KAAK,MAAM;YAC1B,MAAM,IAAIW,MAAM;QAClB;QAEA,IAAI,OAAO,IAAI,CAACX,QAAQ,KAAK,UAAU;YACrC,IAAI,CAACU,QAAQ;gBACX,MAAM,IAAIC,MACR;YAEJ;YAEA,OAAOC,IAAAA,oCAAc,EAAC,IAAI,CAACC,QAAQ;QACrC;QAEA,OAAO,IAAI,CAACb,QAAQ;IACtB;IAEA;;;GAGC,GACD,IAAYa,WAAuC;QACjD,IAAI,IAAI,CAACb,QAAQ,KAAK,MAAM;YAC1B,MAAM,IAAIW,MAAM;QAClB;QACA,IAAI,OAAO,IAAI,CAACX,QAAQ,KAAK,UAAU;YACrC,MAAM,IAAIW,MAAM;QAClB;QAEA,oEAAoE;QACpE,IAAIG,MAAMC,OAAO,CAAC,IAAI,CAACf,QAAQ,GAAG;YAChC,OAAOgB,IAAAA,kCAAY,KAAI,IAAI,CAAChB,QAAQ;QACtC;QAEA,OAAO,IAAI,CAACA,QAAQ;IACtB;IAEA;;;;;;;GAOC,GACD,AAAOiB,MAAMJ,QAAoC,EAAE;QACjD,IAAI,IAAI,CAACb,QAAQ,KAAK,MAAM;YAC1B,MAAM,IAAIW,MAAM;QAClB;QAEA,mEAAmE;QACnE,IAAIO;QACJ,IAAI,OAAO,IAAI,CAAClB,QAAQ,KAAK,UAAU;YACrCkB,YAAY;gBAACC,IAAAA,sCAAgB,EAAC,IAAI,CAACnB,QAAQ;aAAE;QAC/C,OAAO,IAAIc,MAAMC,OAAO,CAAC,IAAI,CAACf,QAAQ,GAAG;YACvCkB,YAAY,IAAI,CAAClB,QAAQ;QAC3B,OAAO;YACLkB,YAAY;gBAAC,IAAI,CAAClB,QAAQ;aAAC;QAC7B;QAEA,mCAAmC;QACnCkB,UAAUE,IAAI,CAACP;QAEf,uBAAuB;QACvB,IAAI,CAACb,QAAQ,GAAGkB;IAClB;IAEA;;;;;GAKC,GACD,MAAaG,OAAOC,QAAoC,EAAiB;QACvE,IAAI;YACF,MAAM,IAAI,CAACT,QAAQ,CAACQ,MAAM,CAACC;QAC7B,EAAE,OAAOC,KAAK;YACZ,yDAAyD;YACzD,IAAI,CAACC,IAAAA,0BAAY,EAACD,MAAM;gBACtB,MAAMA;YACR;QACF,SAAU;YACR,IAAI,IAAI,CAACrB,SAAS,EAAE;gBAClB,MAAM,IAAI,CAACA,SAAS;YACtB;QACF;IACF;IAEA;;;;;GAKC,GACD,MAAauB,mBAAmBC,GAAmB,EAAE;QACnD,IAAI;YACF,MAAMD,IAAAA,gCAAkB,EAAC,IAAI,CAACZ,QAAQ,EAAEa;QAC1C,SAAU;YACR,IAAI,IAAI,CAACxB,SAAS,EAAE;gBAClB,MAAM,IAAI,CAACA,SAAS;YACtB;QACF;IACF;AACF"}