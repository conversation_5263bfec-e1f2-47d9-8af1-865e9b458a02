{"version": 3, "sources": ["../../src/server/send-response.ts"], "names": ["sendResponse", "req", "res", "response", "waitUntil", "process", "env", "NEXT_RUNTIME", "statusCode", "status", "statusMessage", "statusText", "headers", "for<PERSON>ach", "value", "name", "toLowerCase", "cookie", "splitCookiesString", "append<PERSON><PERSON>er", "originalResponse", "body", "method", "pipeToNodeResponse", "end"], "mappings": ";;;;+BAasBA;;;eAAAA;;;8BAVa;uBACA;AAS5B,eAAeA,aACpBC,GAAoB,EACpBC,GAAqB,EACrBC,QAAkB,EAClBC,SAAwB;IAExB,4BAA4B;IAC5B,IAAIC,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;YAKvC,kCAAkC;QAClCJ;QALA,iCAAiC;QACjCD,IAAIM,UAAU,GAAGL,SAASM,MAAM;QAChCP,IAAIQ,aAAa,GAAGP,SAASQ,UAAU;SAGvCR,oBAAAA,SAASS,OAAO,qBAAhBT,kBAAkBU,OAAO,CAAC,CAACC,OAAOC;YAChC,yDAAyD;YACzD,IAAIA,KAAKC,WAAW,OAAO,cAAc;gBACvC,qFAAqF;gBACrF,KAAK,MAAMC,UAAUC,IAAAA,yBAAkB,EAACJ,OAAQ;oBAC9CZ,IAAIiB,YAAY,CAACJ,MAAME;gBACzB;YACF,OAAO;gBACLf,IAAIiB,YAAY,CAACJ,MAAMD;YACzB;QACF;QAEA;;;;;KAKC,GAED,MAAMM,mBAAmB,AAAClB,IAAyBkB,gBAAgB;QAEnE,qGAAqG;QACrG,IAAIjB,SAASkB,IAAI,IAAIpB,IAAIqB,MAAM,KAAK,QAAQ;YAC1C,IAAI;gBACF,MAAMC,IAAAA,gCAAkB,EAACpB,SAASkB,IAAI,EAAED;YAC1C,SAAU;gBACR,IAAIhB,WAAW;oBACb,MAAMA;gBACR;YACF;QACF,OAAO;YACLgB,iBAAiBI,GAAG;QACtB;IACF;AACF"}