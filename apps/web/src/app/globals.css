@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600;700&display=swap');

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: 'Inter', system-ui, sans-serif;
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
}

a {
  color: inherit;
  text-decoration: none;
}

@media (prefers-color-scheme: dark) {
  html {
    color-scheme: dark;
  }
}

/* Custom scrollbar for betting platform */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #1e293b;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #475569;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}

/* Betting platform specific styles */
.betting-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.casino-gradient {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.dark .glass-effect {
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Odds animation */
.odds-up {
  animation: oddsUp 0.5s ease-in-out;
}

.odds-down {
  animation: oddsDown 0.5s ease-in-out;
}

@keyframes oddsUp {
  0% { background-color: transparent; }
  50% { background-color: rgba(34, 197, 94, 0.3); }
  100% { background-color: transparent; }
}

@keyframes oddsDown {
  0% { background-color: transparent; }
  50% { background-color: rgba(239, 68, 68, 0.3); }
  100% { background-color: transparent; }
}

/* Betting slip animations */
.bet-slip-enter {
  animation: betSlipEnter 0.3s ease-out;
}

@keyframes betSlipEnter {
  0% {
    transform: translateX(100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Loading shimmer effect */
.shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

.dark .shimmer {
  background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
  background-size: 200% 100%;
}

/* Pulse effect for live indicators */
.pulse-live {
  animation: pulseLive 2s infinite;
}

@keyframes pulseLive {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Custom button styles */
.btn-primary {
  @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200 transform hover:scale-105 active:scale-95;
}

.btn-secondary {
  @apply bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200;
}

.btn-success {
  @apply bg-success-600 hover:bg-success-700 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200;
}

.btn-danger {
  @apply bg-danger-600 hover:bg-danger-700 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200;
}

/* Card styles */
.card {
  @apply bg-white dark:bg-dark-800 rounded-xl shadow-lg border border-gray-200 dark:border-dark-700 p-6;
}

.card-hover {
  @apply card transition-all duration-300 hover:shadow-xl hover:scale-105 cursor-pointer;
}

/* Input styles */
.input-primary {
  @apply w-full px-4 py-2 border border-gray-300 dark:border-dark-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-dark-700 text-gray-900 dark:text-white;
}

/* Responsive design helpers */
@media (max-width: 768px) {
  .mobile-hidden {
    display: none;
  }
}

@media (min-width: 769px) {
  .mobile-only {
    display: none;
  }
}

/* Accessibility - Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .animate-pulse,
  .animate-bounce,
  .animate-spin {
    animation: none !important;
  }
}

/* Layout alignment utilities */
.betting-container {
  @apply container mx-auto px-4 lg:px-6 xl:px-8 max-w-7xl;
}

.betting-card {
  @apply bg-dark-800 rounded-xl border border-dark-700 hover:border-dark-600 transition-all duration-300;
}

.betting-grid {
  @apply grid gap-4 lg:gap-6;
}

.betting-grid-2 {
  @apply grid grid-cols-2 gap-4 lg:gap-6;
}

.betting-grid-3 {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 lg:gap-6;
}

.betting-grid-4 {
  @apply grid grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6;
}

.betting-section {
  @apply mb-8;
}

.betting-section-spacing {
  @apply space-y-6;
}

/* Consistent text alignment */
.text-balance {
  text-wrap: balance;
}

/* Consistent button heights */
.btn-primary {
  @apply bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 h-12 flex items-center justify-center;
}

.btn-secondary {
  @apply border-2 border-white text-white hover:bg-white hover:text-primary-700 px-6 py-3 rounded-xl font-semibold transition-all duration-300 h-12 flex items-center justify-center;
}

/* Consistent card heights */
.card-equal-height {
  @apply h-full flex flex-col;
}

.card-content-grow {
  @apply flex-1 flex flex-col justify-between;
}
