'use client'

import { motion } from 'framer-motion'
import { 
  UsersIcon,
  CurrencyDollarIcon,
  TrophyIcon,
  FireIcon,
} from '@heroicons/react/24/outline'
import { mockPlatformStats, mockRecentWinners } from '@/data/mock-data'

const stats = [
  {
    id: 'users',
    label: 'Active Users',
    value: mockPlatformStats.totalUsers.toLocaleString(),
    change: '+12.5%',
    changeType: 'positive' as const,
    icon: UsersIcon,
    color: 'from-blue-500 to-blue-600',
  },
  {
    id: 'volume',
    label: 'Total Volume',
    value: `$${(mockPlatformStats.totalVolume / 1000000).toFixed(1)}M`,
    change: '+8.2%',
    changeType: 'positive' as const,
    icon: CurrencyDollarIcon,
    color: 'from-green-500 to-green-600',
  },
  {
    id: 'bets',
    label: 'Total Bets',
    value: (mockPlatformStats.totalBets / 1000000).toFixed(1) + 'M',
    change: '+15.3%',
    changeType: 'positive' as const,
    icon: TrophyIcon,
    color: 'from-purple-500 to-purple-600',
  },
  {
    id: 'live',
    label: 'Live Events',
    value: mockPlatformStats.liveEvents.toString(),
    change: '+5',
    changeType: 'positive' as const,
    icon: FireIcon,
    color: 'from-red-500 to-red-600',
  },
]

export function StatsOverview() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6 mb-8">
      {stats.map((stat, index) => (
        <motion.div
          key={stat.id}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: index * 0.1 }}
          className="bg-dark-800 rounded-xl p-6 border border-dark-700 hover:border-dark-600 transition-colors"
        >
          <div className="flex items-center justify-between mb-4">
            <div className={`p-3 rounded-lg bg-gradient-to-r ${stat.color}`}>
              <stat.icon className="h-6 w-6 text-white" />
            </div>
            <div className={`text-sm font-medium ${
              stat.changeType === 'positive' ? 'text-green-400' : 'text-red-400'
            }`}>
              {stat.change}
            </div>
          </div>
          
          <div>
            <h3 className="text-2xl font-bold text-white mb-1">
              {stat.value}
            </h3>
            <p className="text-gray-400 text-sm">
              {stat.label}
            </p>
          </div>
        </motion.div>
      ))}
      
      {/* Recent Winners */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.4 }}
        className="md:col-span-2 xl:col-span-4 bg-dark-800 rounded-xl p-6 border border-dark-700"
      >
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-white flex items-center gap-2">
            <TrophyIcon className="h-5 w-5 text-yellow-400" />
            Recent Big Winners
          </h3>
          <span className="text-sm text-gray-400">Live updates</span>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {mockRecentWinners.map((winner, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.4, delay: 0.5 + index * 0.1 }}
              className="bg-dark-700 rounded-lg p-4 border border-dark-600"
            >
              <div className="flex items-center justify-between mb-2">
                <span className="text-white font-medium">
                  {winner.username}
                </span>
                <span className="text-green-400 font-bold">
                  ${winner.amount.toLocaleString()}
                </span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-400">{winner.game}</span>
                <span className="text-gray-500">{winner.time}</span>
              </div>
            </motion.div>
          ))}
        </div>
      </motion.div>
    </div>
  )
}
