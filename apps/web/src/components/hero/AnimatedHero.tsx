'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  SparklesIcon, 
  CurrencyDollarIcon,
  TrophyIcon,
  FireIcon,
  BoltIcon,
  StarIcon
} from '@heroicons/react/24/outline'

// Floating elements data
const floatingElements = [
  { id: 1, icon: SparklesIcon, delay: 0, x: '10%', y: '20%', duration: 3 },
  { id: 2, icon: CurrencyDollarIcon, delay: 0.5, x: '80%', y: '30%', duration: 4 },
  { id: 3, icon: TrophyIcon, delay: 1, x: '15%', y: '70%', duration: 3.5 },
  { id: 4, icon: FireIcon, delay: 1.5, x: '85%', y: '60%', duration: 2.8 },
  { id: 5, icon: BoltIcon, delay: 2, x: '50%', y: '15%', duration: 3.2 },
  { id: 6, icon: StarIcon, delay: 2.5, x: '70%', y: '80%', duration: 3.8 },
]

// Live odds ticker data
const liveOdds = [
  { team1: 'Lakers', team2: 'Warriors', odds: '2.45', change: '+0.15' },
  { team1: '<PERSON>', team2: 'Arsenal', odds: '1.85', change: '-0.05' },
  { team1: 'Cowboys', team2: 'Giants', odds: '3.20', change: '+0.25' },
  { team1: 'Real Madrid', team2: 'Barcelona', odds: '2.10', change: '+0.10' },
  { team1: 'Celtics', team2: 'Heat', odds: '1.95', change: '-0.15' },
]

// Animated counter component
function AnimatedCounter({ end, duration = 2, prefix = '', suffix = '' }: {
  end: number
  duration?: number
  prefix?: string
  suffix?: string
}) {
  const [count, setCount] = useState(0)

  useEffect(() => {
    let startTime: number
    let animationFrame: number

    const animate = (timestamp: number) => {
      if (!startTime) startTime = timestamp
      const progress = Math.min((timestamp - startTime) / (duration * 1000), 1)
      
      setCount(Math.floor(progress * end))
      
      if (progress < 1) {
        animationFrame = requestAnimationFrame(animate)
      }
    }

    animationFrame = requestAnimationFrame(animate)
    return () => cancelAnimationFrame(animationFrame)
  }, [end, duration])

  return <span>{prefix}{count.toLocaleString()}{suffix}</span>
}

// Typewriter effect component
function TypewriterText({ text, delay = 0 }: { text: string; delay?: number }) {
  const [displayText, setDisplayText] = useState('')
  const [currentIndex, setCurrentIndex] = useState(0)

  useEffect(() => {
    const timer = setTimeout(() => {
      if (currentIndex < text.length) {
        setDisplayText(prev => prev + text[currentIndex])
        setCurrentIndex(prev => prev + 1)
      }
    }, delay + currentIndex * 100)

    return () => clearTimeout(timer)
  }, [currentIndex, text, delay])

  return (
    <span>
      {displayText}
      <motion.span
        animate={{ opacity: [1, 0] }}
        transition={{ duration: 0.8, repeat: Infinity, repeatType: 'reverse' }}
        className="inline-block w-1 h-8 bg-yellow-400 ml-1"
      />
    </span>
  )
}

export function AnimatedHero() {
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) {
    return (
      <div className="relative overflow-hidden rounded-2xl bg-gradient-to-r from-primary-600 via-primary-700 to-secondary-600 p-8 text-white h-96">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative z-10">
          <h1 className="text-4xl lg:text-6xl font-bold mb-4">
            Welcome to <span className="text-yellow-400">Kesar Mango</span>
          </h1>
          <p className="text-xl lg:text-2xl mb-6 opacity-90">
            Experience the ultimate betting platform with live odds and instant payouts
          </p>
        </div>
      </div>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      className="relative overflow-hidden rounded-2xl bg-gradient-to-r from-primary-600 via-primary-700 to-secondary-600 p-6 sm:p-8 lg:p-12 text-white"
    >
      {/* Animated background gradient */}
      <motion.div
        animate={{
          background: [
            'linear-gradient(45deg, #667eea, #764ba2)',
            'linear-gradient(45deg, #764ba2, #667eea)',
            'linear-gradient(45deg, #667eea, #764ba2)',
          ]
        }}
        transition={{ duration: 8, repeat: Infinity, ease: 'linear' }}
        className="absolute inset-0"
      />
      
      {/* Overlay */}
      <div className="absolute inset-0 bg-black/20"></div>
      
      {/* Floating elements */}
      <div className="absolute inset-0 overflow-hidden">
        {floatingElements.map((element) => {
          const IconComponent = element.icon
          return (
            <motion.div
              key={element.id}
              initial={{ opacity: 0, scale: 0 }}
              animate={{ 
                opacity: [0, 1, 1, 0],
                scale: [0, 1, 1, 0],
                y: [0, -20, 0, -20],
                rotate: [0, 360]
              }}
              transition={{
                duration: element.duration,
                delay: element.delay,
                repeat: Infinity,
                repeatDelay: 2,
                ease: 'easeInOut'
              }}
              className="absolute"
              style={{ left: element.x, top: element.y }}
            >
              <IconComponent className="h-6 w-6 sm:h-8 sm:w-8 text-yellow-400/60" />
            </motion.div>
          )
        })}
      </div>

      {/* Particle effects */}
      <div className="absolute inset-0">
        {Array.from({ length: 20 }).map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-yellow-400/30 rounded-full"
            initial={{ 
              x: Math.random() * 100 + '%',
              y: '100%',
              opacity: 0
            }}
            animate={{
              y: '-10%',
              opacity: [0, 1, 0],
            }}
            transition={{
              duration: Math.random() * 3 + 2,
              delay: Math.random() * 5,
              repeat: Infinity,
              ease: 'linear'
            }}
          />
        ))}
      </div>

      {/* Main content */}
      <div className="relative z-10">
        <motion.h1 
          className="text-3xl sm:text-4xl lg:text-6xl xl:text-7xl font-bold mb-4 lg:mb-6"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          Welcome to <span className="text-yellow-400">
            <TypewriterText text="Kesar Mango" delay={1000} />
          </span>
        </motion.h1>
        
        <motion.p 
          className="text-lg sm:text-xl lg:text-2xl mb-6 lg:mb-8 opacity-90 max-w-3xl"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
        >
          Experience the ultimate betting platform with live odds and instant payouts
        </motion.p>

        {/* Animated stats */}
        <motion.div 
          className="grid grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6 mb-6 lg:mb-8"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
        >
          <div className="text-center">
            <div className="text-2xl lg:text-3xl font-bold text-yellow-400">
              <AnimatedCounter end={125847} prefix="" suffix="" />
            </div>
            <div className="text-sm lg:text-base opacity-80">Active Users</div>
          </div>
          <div className="text-center">
            <div className="text-2xl lg:text-3xl font-bold text-green-400">
              $<AnimatedCounter end={45} prefix="" suffix=".9M" />
            </div>
            <div className="text-sm lg:text-base opacity-80">Total Volume</div>
          </div>
          <div className="text-center">
            <div className="text-2xl lg:text-3xl font-bold text-blue-400">
              <AnimatedCounter end={2} prefix="" suffix=".8M" />
            </div>
            <div className="text-sm lg:text-base opacity-80">Total Bets</div>
          </div>
          <div className="text-center">
            <div className="text-2xl lg:text-3xl font-bold text-purple-400">
              <AnimatedCounter end={156} prefix="" suffix="" />
            </div>
            <div className="text-sm lg:text-base opacity-80">Live Events</div>
          </div>
        </motion.div>

        {/* CTA Buttons */}
        <motion.div 
          className="flex flex-col sm:flex-row gap-4"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.8 }}
        >
          <motion.button
            whileHover={{ scale: 1.05, boxShadow: '0 0 30px rgba(255, 255, 255, 0.3)' }}
            whileTap={{ scale: 0.95 }}
            className="bg-white text-primary-700 px-6 sm:px-8 py-3 sm:py-4 rounded-xl font-semibold hover:bg-gray-100 transition-all duration-300 relative overflow-hidden group"
          >
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-yellow-400 to-orange-500 opacity-0 group-hover:opacity-20 transition-opacity duration-300"
              initial={false}
            />
            <span className="relative z-10">Start Betting Now</span>
          </motion.button>
          
          <motion.button
            whileHover={{ scale: 1.05, boxShadow: '0 0 30px rgba(255, 255, 255, 0.2)' }}
            whileTap={{ scale: 0.95 }}
            className="border-2 border-white text-white px-6 sm:px-8 py-3 sm:py-4 rounded-xl font-semibold hover:bg-white hover:text-primary-700 transition-all duration-300 relative overflow-hidden group"
          >
            <motion.div
              className="absolute inset-0 bg-white opacity-0 group-hover:opacity-100 transition-opacity duration-300"
              initial={false}
            />
            <span className="relative z-10">Explore Casino</span>
          </motion.button>
        </motion.div>
      </div>

      {/* Live odds ticker */}
      <motion.div
        className="absolute bottom-0 left-0 right-0 bg-black/30 backdrop-blur-sm border-t border-white/10 p-2 overflow-hidden"
        initial={{ y: 100 }}
        animate={{ y: 0 }}
        transition={{ duration: 0.8, delay: 1 }}
      >
        <motion.div
          className="flex space-x-8 whitespace-nowrap"
          animate={{ x: [0, -100 * liveOdds.length] }}
          transition={{ duration: 20, repeat: Infinity, ease: 'linear' }}
        >
          {[...liveOdds, ...liveOdds].map((odd, index) => (
            <div key={index} className="flex items-center space-x-2 text-sm">
              <span className="text-white">{odd.team1} vs {odd.team2}</span>
              <span className="text-yellow-400 font-bold">{odd.odds}</span>
              <span className={`text-xs ${odd.change.startsWith('+') ? 'text-green-400' : 'text-red-400'}`}>
                {odd.change}
              </span>
            </div>
          ))}
        </motion.div>
      </motion.div>
    </motion.div>
  )
}
