'use client'

import { motion } from 'framer-motion'
import { TrendingUpIcon, FireIcon } from '@heroicons/react/24/outline'
import { mockTrendingBets } from '@/data/mock-data'

export function TrendingBets() {
  return (
    <div className="bg-dark-800 rounded-xl border border-dark-700 p-6">
      <div className="flex items-center space-x-2 mb-6">
        <TrendingUpIcon className="h-5 w-5 text-primary-400" />
        <h3 className="text-lg font-semibold text-white">Trending Bets</h3>
        <FireIcon className="h-4 w-4 text-orange-400" />
      </div>

      <div className="space-y-4">
        {mockTrendingBets.map((bet, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.4, delay: index * 0.1 }}
            className="bg-dark-700 rounded-lg p-4 border border-dark-600 hover:border-primary-500/50 transition-colors cursor-pointer group"
          >
            <div className="flex items-start justify-between mb-2">
              <div className="flex-1">
                <h4 className="text-white font-medium text-sm group-hover:text-primary-400 transition-colors">
                  {bet.event}
                </h4>
                <p className="text-gray-400 text-xs mt-1">
                  {bet.selection}
                </p>
              </div>
              <span className="text-primary-400 font-bold">
                {bet.odds.toFixed(2)}
              </span>
            </div>
            
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <div className="w-full bg-dark-600 rounded-full h-2 flex-1 max-w-[100px]">
                  <motion.div
                    initial={{ width: 0 }}
                    animate={{ width: `${bet.percentage}%` }}
                    transition={{ duration: 1, delay: 0.5 + index * 0.1 }}
                    className="bg-gradient-to-r from-primary-500 to-secondary-500 h-2 rounded-full"
                  />
                </div>
                <span className="text-gray-400 text-xs">
                  {bet.percentage}%
                </span>
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      <motion.button
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        className="w-full mt-4 bg-primary-600 hover:bg-primary-700 text-white py-2 rounded-lg font-medium transition-colors"
      >
        View All Trends
      </motion.button>
    </div>
  )
}
