'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { 
  SparklesIcon, 
  PlayIcon,
  StarIcon,
  FireIcon,
} from '@heroicons/react/24/outline'
import { mockCasinoGames } from '@/data/mock-data'

export function CasinoLobby() {
  const [selectedCategory, setSelectedCategory] = useState('all')

  const categories = [
    { id: 'all', label: 'All Games', count: mockCasinoGames.length },
    { id: 'slots', label: 'Slots', count: mockCasinoGames.filter(g => g.category === 'Slots').length },
    { id: 'live', label: 'Live Casino', count: mockCasinoGames.filter(g => g.isLive).length },
    { id: 'featured', label: 'Featured', count: mockCasinoGames.filter(g => g.isFeatured).length },
  ]

  const filteredGames = mockCasinoGames.filter(game => {
    if (selectedCategory === 'all') return true
    if (selectedCategory === 'slots') return game.category === 'Slots'
    if (selectedCategory === 'live') return game.isLive
    if (selectedCategory === 'featured') return game.isFeatured
    return true
  })

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-3">
        <SparklesIcon className="h-6 w-6 text-purple-400" />
        <h2 className="text-2xl font-bold text-white">Casino Games</h2>
      </div>

      {/* Category Filter */}
      <div className="flex flex-wrap gap-2">
        {categories.map((category) => (
          <motion.button
            key={category.id}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => setSelectedCategory(category.id)}
            className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
              selectedCategory === category.id
                ? 'bg-purple-600 text-white'
                : 'bg-dark-700 text-gray-300 hover:bg-dark-600 hover:text-white'
            }`}
          >
            <span>{category.label}</span>
            <span className="text-xs bg-black/20 px-2 py-1 rounded-full">
              {category.count}
            </span>
          </motion.button>
        ))}
      </div>

      {/* Games Grid */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
        {filteredGames.map((game, index) => (
          <motion.div
            key={game.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4, delay: index * 0.05 }}
            whileHover={{ scale: 1.05 }}
            className="group cursor-pointer"
          >
            <div className="relative bg-dark-800 rounded-xl overflow-hidden border border-dark-700 hover:border-purple-500/50 transition-all duration-300">
              {/* Game Thumbnail */}
              <div className="aspect-square bg-gradient-to-br from-purple-500/20 to-pink-500/20 flex items-center justify-center relative overflow-hidden">
                {/* Placeholder for game thumbnail */}
                <div className="w-full h-full bg-gradient-to-br from-purple-600 to-pink-600 flex items-center justify-center">
                  <SparklesIcon className="h-12 w-12 text-white/80" />
                </div>
                
                {/* Badges */}
                <div className="absolute top-2 left-2 flex flex-col space-y-1">
                  {game.isNew && (
                    <span className="bg-green-500 text-white text-xs px-2 py-1 rounded-full font-medium">
                      NEW
                    </span>
                  )}
                  {game.isFeatured && (
                    <span className="bg-yellow-500 text-black text-xs px-2 py-1 rounded-full font-medium">
                      ⭐ FEATURED
                    </span>
                  )}
                  {game.isLive && (
                    <span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full font-medium flex items-center space-x-1">
                      <div className="w-1 h-1 bg-white rounded-full animate-pulse"></div>
                      <span>LIVE</span>
                    </span>
                  )}
                </div>

                {/* Play Button Overlay */}
                <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                  <motion.div
                    initial={{ scale: 0 }}
                    whileHover={{ scale: 1 }}
                    className="w-16 h-16 bg-white rounded-full flex items-center justify-center"
                  >
                    <PlayIcon className="h-8 w-8 text-purple-600 ml-1" />
                  </motion.div>
                </div>

                {/* RTP Badge */}
                <div className="absolute top-2 right-2">
                  <span className="bg-black/70 text-white text-xs px-2 py-1 rounded-full">
                    RTP {game.rtp}%
                  </span>
                </div>
              </div>

              {/* Game Info */}
              <div className="p-3">
                <h3 className="text-white font-medium text-sm mb-1 group-hover:text-purple-400 transition-colors">
                  {game.name}
                </h3>
                <p className="text-gray-400 text-xs mb-2">{game.provider}</p>
                
                <div className="flex items-center justify-between text-xs">
                  <span className="text-gray-500">
                    ${game.minBet} - ${game.maxBet}
                  </span>
                  <div className="flex items-center space-x-1">
                    <StarIcon className="h-3 w-3 text-yellow-400" />
                    <span className="text-gray-400">{game.popularity}</span>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Load More Button */}
      <div className="text-center">
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-8 py-3 rounded-xl font-semibold transition-all duration-200"
        >
          Load More Games
        </motion.button>
      </div>

      {/* Jackpot Banner */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.3 }}
        className="bg-gradient-to-r from-yellow-500 via-orange-500 to-red-500 rounded-xl p-6 text-white relative overflow-hidden"
      >
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative z-10">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-2xl font-bold mb-2">🎰 Progressive Jackpot</h3>
              <p className="text-lg opacity-90">Current jackpot amount</p>
            </div>
            <div className="text-right">
              <div className="text-4xl font-bold">$2,847,593</div>
              <div className="text-sm opacity-75">and growing...</div>
            </div>
          </div>
        </div>
        
        {/* Animated background elements */}
        <div className="absolute top-4 right-4 w-16 h-16 bg-white/10 rounded-full animate-pulse"></div>
        <div className="absolute bottom-4 left-20 w-8 h-8 bg-yellow-400/20 rounded-full animate-bounce"></div>
      </motion.div>
    </div>
  )
}
