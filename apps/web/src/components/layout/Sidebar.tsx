'use client'

import { motion, AnimatePresence } from 'framer-motion'
import { 
  HomeIcon,
  TrophyIcon,
  PlayIcon,
  SparklesIcon,
  ChartBarIcon,
  CogIcon,
  XMarkIcon,
} from '@heroicons/react/24/outline'
import { mockSports } from '@/data/mock-data'

interface SidebarProps {
  isOpen: boolean
  onClose: () => void
}

const navigationItems = [
  { id: 'home', label: 'Home', icon: HomeIcon, href: '/' },
  { id: 'sports', label: 'Sports', icon: TrophyIcon, href: '/sports' },
  { id: 'live', label: 'Live Betting', icon: PlayIcon, href: '/live', badge: '156' },
  { id: 'casino', label: 'Casino', icon: SparklesIcon, href: '/casino' },
  { id: 'statistics', label: 'Statistics', icon: ChartBarIcon, href: '/stats' },
  { id: 'settings', label: 'Settings', icon: CogIcon, href: '/settings' },
]

export function Sidebar({ isOpen, onClose }: SidebarProps) {
  return (
    <>
      {/* Mobile overlay */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={onClose}
            className="fixed inset-0 bg-black/50 z-40 lg:hidden"
          />
        )}
      </AnimatePresence>

      {/* Sidebar */}
      <motion.aside
        initial={{ x: -320 }}
        animate={{ x: isOpen ? 0 : -320 }}
        transition={{ type: 'spring', damping: 25, stiffness: 200 }}
        className="fixed left-0 top-0 h-full w-80 bg-dark-900 border-r border-dark-700 z-50 lg:translate-x-0 lg:static lg:z-auto"
      >
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-dark-700">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-xl flex items-center justify-center">
                <span className="text-white font-bold text-xl">K</span>
              </div>
              <div>
                <h2 className="text-white font-bold text-lg">Kesar Mango</h2>
                <p className="text-gray-400 text-sm">Premium Betting</p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="lg:hidden p-2 rounded-lg hover:bg-dark-700 transition-colors"
            >
              <XMarkIcon className="h-6 w-6 text-gray-400" />
            </button>
          </div>

          {/* Navigation */}
          <nav className="flex-1 p-4 space-y-2">
            <div className="mb-6">
              <h3 className="text-gray-400 text-sm font-medium mb-3 uppercase tracking-wider">
                Navigation
              </h3>
              {navigationItems.map((item) => (
                <motion.a
                  key={item.id}
                  href={item.href}
                  whileHover={{ x: 4 }}
                  className="flex items-center justify-between p-3 rounded-lg hover:bg-dark-700 transition-colors group"
                >
                  <div className="flex items-center space-x-3">
                    <item.icon className="h-5 w-5 text-gray-400 group-hover:text-primary-400 transition-colors" />
                    <span className="text-gray-300 group-hover:text-white transition-colors">
                      {item.label}
                    </span>
                  </div>
                  {item.badge && (
                    <span className="bg-primary-600 text-white text-xs px-2 py-1 rounded-full">
                      {item.badge}
                    </span>
                  )}
                </motion.a>
              ))}
            </div>

            {/* Sports Quick Access */}
            <div>
              <h3 className="text-gray-400 text-sm font-medium mb-3 uppercase tracking-wider">
                Popular Sports
              </h3>
              <div className="space-y-1">
                {mockSports.slice(0, 6).map((sport) => (
                  <motion.a
                    key={sport.id}
                    href={`/sports/${sport.slug}`}
                    whileHover={{ x: 4 }}
                    className="flex items-center justify-between p-2 rounded-lg hover:bg-dark-700 transition-colors group"
                  >
                    <div className="flex items-center space-x-3">
                      <span className="text-lg">{sport.icon}</span>
                      <span className="text-gray-300 group-hover:text-white transition-colors text-sm">
                        {sport.name}
                      </span>
                    </div>
                    <span className="text-gray-500 text-xs">
                      {sport.eventCount}
                    </span>
                  </motion.a>
                ))}
              </div>
            </div>
          </nav>

          {/* Promotions Banner */}
          <div className="p-4">
            <motion.div
              whileHover={{ scale: 1.02 }}
              className="bg-gradient-to-r from-primary-600 to-secondary-600 rounded-xl p-4 text-white cursor-pointer"
            >
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center">
                  <SparklesIcon className="h-6 w-6" />
                </div>
                <div className="flex-1">
                  <h4 className="font-semibold text-sm">Welcome Bonus</h4>
                  <p className="text-xs opacity-90">Get 100% up to $500</p>
                </div>
              </div>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="w-full mt-3 bg-white text-primary-700 py-2 rounded-lg text-sm font-semibold hover:bg-gray-100 transition-colors"
              >
                Claim Now
              </motion.button>
            </motion.div>
          </div>

          {/* Footer */}
          <div className="p-4 border-t border-dark-700">
            <div className="text-center text-gray-500 text-xs">
              <p>© 2024 Kesar Mango</p>
              <p>Licensed & Regulated</p>
            </div>
          </div>
        </div>
      </motion.aside>
    </>
  )
}
