"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/StatsOverview.tsx":
/*!****************************************************!*\
  !*** ./src/components/dashboard/StatsOverview.tsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StatsOverview: function() { return /* binding */ StatsOverview; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/../../node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_CurrencyDollarIcon_FireIcon_TrophyIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=CurrencyDollarIcon,FireIcon,TrophyIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/../../node_modules/@heroicons/react/24/outline/esm/UsersIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CurrencyDollarIcon_FireIcon_TrophyIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CurrencyDollarIcon,FireIcon,TrophyIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/../../node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CurrencyDollarIcon_FireIcon_TrophyIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CurrencyDollarIcon,FireIcon,TrophyIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/../../node_modules/@heroicons/react/24/outline/esm/TrophyIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CurrencyDollarIcon_FireIcon_TrophyIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CurrencyDollarIcon,FireIcon,TrophyIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/../../node_modules/@heroicons/react/24/outline/esm/FireIcon.js\");\n/* harmony import */ var _data_mock_data__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/data/mock-data */ \"(app-pages-browser)/./src/data/mock-data.ts\");\n/* __next_internal_client_entry_do_not_use__ StatsOverview auto */ \n\n\n\nconst stats = [\n    {\n        id: \"users\",\n        label: \"Active Users\",\n        value: _data_mock_data__WEBPACK_IMPORTED_MODULE_1__.mockPlatformStats.totalUsers.toLocaleString(),\n        change: \"+12.5%\",\n        changeType: \"positive\",\n        icon: _barrel_optimize_names_CurrencyDollarIcon_FireIcon_TrophyIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        color: \"from-blue-500 to-blue-600\"\n    },\n    {\n        id: \"volume\",\n        label: \"Total Volume\",\n        value: \"$\".concat((_data_mock_data__WEBPACK_IMPORTED_MODULE_1__.mockPlatformStats.totalVolume / 1000000).toFixed(1), \"M\"),\n        change: \"+8.2%\",\n        changeType: \"positive\",\n        icon: _barrel_optimize_names_CurrencyDollarIcon_FireIcon_TrophyIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        color: \"from-green-500 to-green-600\"\n    },\n    {\n        id: \"bets\",\n        label: \"Total Bets\",\n        value: (_data_mock_data__WEBPACK_IMPORTED_MODULE_1__.mockPlatformStats.totalBets / 1000000).toFixed(1) + \"M\",\n        change: \"+15.3%\",\n        changeType: \"positive\",\n        icon: _barrel_optimize_names_CurrencyDollarIcon_FireIcon_TrophyIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        color: \"from-purple-500 to-purple-600\"\n    },\n    {\n        id: \"live\",\n        label: \"Live Events\",\n        value: _data_mock_data__WEBPACK_IMPORTED_MODULE_1__.mockPlatformStats.liveEvents.toString(),\n        change: \"+5\",\n        changeType: \"positive\",\n        icon: _barrel_optimize_names_CurrencyDollarIcon_FireIcon_TrophyIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        color: \"from-red-500 to-red-600\"\n    }\n];\nfunction StatsOverview() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6 mb-8\",\n        children: [\n            stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.6,\n                        delay: index * 0.1\n                    },\n                    className: \"bg-dark-800 rounded-xl p-4 lg:p-6 border border-dark-700 hover:border-dark-600 transition-all duration-300 h-full flex flex-col\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-2.5 lg:p-3 rounded-lg bg-gradient-to-r \".concat(stat.color, \" flex-shrink-0\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                        className: \"h-5 w-5 lg:h-6 lg:w-6 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx\",\n                                        lineNumber: 64,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm font-medium \".concat(stat.changeType === \"positive\" ? \"text-green-400\" : \"text-red-400\"),\n                                    children: stat.change\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 flex flex-col justify-end\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl lg:text-2xl font-bold text-white mb-1 leading-tight\",\n                                    children: stat.value\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 text-sm leading-tight\",\n                                    children: stat.label\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, stat.id, true, {\n                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 9\n                }, this)),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.6,\n                    delay: 0.4\n                },\n                className: \"md:col-span-2 xl:col-span-4 bg-dark-800 rounded-xl p-6 border border-dark-700\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-white flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CurrencyDollarIcon_FireIcon_TrophyIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-5 w-5 text-yellow-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Recent Big Winners\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-gray-400\",\n                                children: \"Live updates\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                        children: _data_mock_data__WEBPACK_IMPORTED_MODULE_1__.mockRecentWinners.map((winner, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    x: -20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                transition: {\n                                    duration: 0.4,\n                                    delay: 0.5 + index * 0.1\n                                },\n                                className: \"bg-dark-700 rounded-lg p-4 border border-dark-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-medium\",\n                                                children: winner.username\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-green-400 font-bold\",\n                                                children: [\n                                                    \"$\",\n                                                    winner.amount.toLocaleString()\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-400\",\n                                                children: winner.game\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-500\",\n                                                children: winner.time\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx\",\n        lineNumber: 53,\n        columnNumber: 5\n    }, this);\n}\n_c = StatsOverview;\nvar _c;\n$RefreshReg$(_c, \"StatsOverview\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/StatsOverview.tsx\n"));

/***/ })

});