"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/layout/Sidebar.tsx":
/*!*******************************************!*\
  !*** ./src/components/layout/Sidebar.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: function() { return /* binding */ Sidebar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/../../node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/../../node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_CogIcon_HomeIcon_PlayIcon_SparklesIcon_TrophyIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,CogIcon,HomeIcon,PlayIcon,SparklesIcon,TrophyIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/../../node_modules/@heroicons/react/24/outline/esm/HomeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_CogIcon_HomeIcon_PlayIcon_SparklesIcon_TrophyIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,CogIcon,HomeIcon,PlayIcon,SparklesIcon,TrophyIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/../../node_modules/@heroicons/react/24/outline/esm/TrophyIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_CogIcon_HomeIcon_PlayIcon_SparklesIcon_TrophyIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,CogIcon,HomeIcon,PlayIcon,SparklesIcon,TrophyIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/../../node_modules/@heroicons/react/24/outline/esm/PlayIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_CogIcon_HomeIcon_PlayIcon_SparklesIcon_TrophyIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,CogIcon,HomeIcon,PlayIcon,SparklesIcon,TrophyIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/../../node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_CogIcon_HomeIcon_PlayIcon_SparklesIcon_TrophyIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,CogIcon,HomeIcon,PlayIcon,SparklesIcon,TrophyIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/../../node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_CogIcon_HomeIcon_PlayIcon_SparklesIcon_TrophyIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,CogIcon,HomeIcon,PlayIcon,SparklesIcon,TrophyIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/../../node_modules/@heroicons/react/24/outline/esm/CogIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_CogIcon_HomeIcon_PlayIcon_SparklesIcon_TrophyIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,CogIcon,HomeIcon,PlayIcon,SparklesIcon,TrophyIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/../../node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _data_mock_data__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/data/mock-data */ \"(app-pages-browser)/./src/data/mock-data.ts\");\n/* __next_internal_client_entry_do_not_use__ Sidebar auto */ \n\n\n\nconst navigationItems = [\n    {\n        id: \"home\",\n        label: \"Home\",\n        icon: _barrel_optimize_names_ChartBarIcon_CogIcon_HomeIcon_PlayIcon_SparklesIcon_TrophyIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        href: \"/\"\n    },\n    {\n        id: \"sports\",\n        label: \"Sports\",\n        icon: _barrel_optimize_names_ChartBarIcon_CogIcon_HomeIcon_PlayIcon_SparklesIcon_TrophyIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        href: \"/sports\"\n    },\n    {\n        id: \"live\",\n        label: \"Live Betting\",\n        icon: _barrel_optimize_names_ChartBarIcon_CogIcon_HomeIcon_PlayIcon_SparklesIcon_TrophyIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        href: \"/live\",\n        badge: \"156\"\n    },\n    {\n        id: \"casino\",\n        label: \"Casino\",\n        icon: _barrel_optimize_names_ChartBarIcon_CogIcon_HomeIcon_PlayIcon_SparklesIcon_TrophyIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        href: \"/casino\"\n    },\n    {\n        id: \"statistics\",\n        label: \"Statistics\",\n        icon: _barrel_optimize_names_ChartBarIcon_CogIcon_HomeIcon_PlayIcon_SparklesIcon_TrophyIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        href: \"/stats\"\n    },\n    {\n        id: \"settings\",\n        label: \"Settings\",\n        icon: _barrel_optimize_names_ChartBarIcon_CogIcon_HomeIcon_PlayIcon_SparklesIcon_TrophyIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        href: \"/settings\"\n    }\n];\nfunction Sidebar(param) {\n    let { isOpen, onClose } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.AnimatePresence, {\n                children: isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    onClick: onClose,\n                    className: \"fixed inset-0 bg-black/50 z-40 lg:hidden\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.aside, {\n                initial: {\n                    x: -320\n                },\n                animate: {\n                    x: isOpen ? 0 : -320\n                },\n                transition: {\n                    type: \"spring\",\n                    damping: 25,\n                    stiffness: 200\n                },\n                className: \"fixed left-0 top-0 h-full w-64 lg:w-64 xl:w-72 2xl:w-80 bg-dark-900 border-r border-dark-700 z-50 lg:translate-x-0 lg:static lg:z-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col h-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-6 border-b border-dark-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-xl flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-bold text-xl\",\n                                                children: \"K\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                                                lineNumber: 57,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                                            lineNumber: 56,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-white font-bold text-lg\",\n                                                    children: \"Kesar Mango\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                                                    lineNumber: 60,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-400 text-sm\",\n                                                    children: \"Premium Betting\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                                                    lineNumber: 61,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onClose,\n                                    className: \"lg:hidden p-2 rounded-lg hover:bg-dark-700 transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_CogIcon_HomeIcon_PlayIcon_SparklesIcon_TrophyIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-6 w-6 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"flex-1 p-4 space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-gray-400 text-sm font-medium mb-3 uppercase tracking-wider\",\n                                            children: \"Navigation\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                                            lineNumber: 75,\n                                            columnNumber: 15\n                                        }, this),\n                                        navigationItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.a, {\n                                                href: item.href,\n                                                whileHover: {\n                                                    x: 4\n                                                },\n                                                className: \"flex items-center justify-between p-3 rounded-lg hover:bg-dark-700 transition-colors group\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                                className: \"h-5 w-5 text-gray-400 group-hover:text-primary-400 transition-colors\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                                                                lineNumber: 86,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-300 group-hover:text-white transition-colors\",\n                                                                children: item.label\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                                                                lineNumber: 87,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                                                        lineNumber: 85,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    item.badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"bg-primary-600 text-white text-xs px-2 py-1 rounded-full\",\n                                                        children: item.badge\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                                                        lineNumber: 92,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, item.id, true, {\n                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                                                lineNumber: 79,\n                                                columnNumber: 17\n                                            }, this))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-gray-400 text-sm font-medium mb-3 uppercase tracking-wider\",\n                                            children: \"Popular Sports\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-1\",\n                                            children: _data_mock_data__WEBPACK_IMPORTED_MODULE_1__.mockSports.slice(0, 6).map((sport)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.a, {\n                                                    href: \"/sports/\".concat(sport.slug),\n                                                    whileHover: {\n                                                        x: 4\n                                                    },\n                                                    className: \"flex items-center justify-between p-2 rounded-lg hover:bg-dark-700 transition-colors group\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-lg\",\n                                                                    children: sport.icon\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                                                                    lineNumber: 114,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-300 group-hover:text-white transition-colors text-sm\",\n                                                                    children: sport.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                                                                    lineNumber: 115,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                                                            lineNumber: 113,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-500 text-xs\",\n                                                            children: sport.eventCount\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                                                            lineNumber: 119,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, sport.id, true, {\n                                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                                                    lineNumber: 107,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                whileHover: {\n                                    scale: 1.02\n                                },\n                                className: \"bg-gradient-to-r from-primary-600 to-secondary-600 rounded-xl p-4 text-white cursor-pointer\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_CogIcon_HomeIcon_PlayIcon_SparklesIcon_TrophyIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"h-6 w-6\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                                                    lineNumber: 136,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-semibold text-sm\",\n                                                        children: \"Welcome Bonus\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                                                        lineNumber: 139,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs opacity-90\",\n                                                        children: \"Get 100% up to $500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                                                        lineNumber: 140,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.button, {\n                                        whileHover: {\n                                            scale: 1.05\n                                        },\n                                        whileTap: {\n                                            scale: 0.95\n                                        },\n                                        className: \"w-full mt-3 bg-white text-primary-700 py-2 rounded-lg text-sm font-semibold hover:bg-gray-100 transition-colors\",\n                                        children: \"Claim Now\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 border-t border-dark-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center text-gray-500 text-xs\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"\\xa9 2024 Kesar Mango\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Licensed & Regulated\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/Sidebar.tsx\n"));

/***/ })

});