"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/casino/CasinoLobby.tsx":
/*!***********************************************!*\
  !*** ./src/components/casino/CasinoLobby.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CasinoLobby: function() { return /* binding */ CasinoLobby; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/../../node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_PlayIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=PlayIcon,SparklesIcon,StarIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/../../node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_PlayIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=PlayIcon,SparklesIcon,StarIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/../../node_modules/@heroicons/react/24/outline/esm/PlayIcon.js\");\n/* harmony import */ var _barrel_optimize_names_PlayIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=PlayIcon,SparklesIcon,StarIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/../../node_modules/@heroicons/react/24/outline/esm/StarIcon.js\");\n/* harmony import */ var _data_mock_data__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/data/mock-data */ \"(app-pages-browser)/./src/data/mock-data.ts\");\n/* __next_internal_client_entry_do_not_use__ CasinoLobby auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction CasinoLobby() {\n    _s();\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const categories = [\n        {\n            id: \"all\",\n            label: \"All Games\",\n            count: _data_mock_data__WEBPACK_IMPORTED_MODULE_2__.mockCasinoGames.length\n        },\n        {\n            id: \"slots\",\n            label: \"Slots\",\n            count: _data_mock_data__WEBPACK_IMPORTED_MODULE_2__.mockCasinoGames.filter((g)=>g.category === \"Slots\").length\n        },\n        {\n            id: \"live\",\n            label: \"Live Casino\",\n            count: _data_mock_data__WEBPACK_IMPORTED_MODULE_2__.mockCasinoGames.filter((g)=>g.isLive).length\n        },\n        {\n            id: \"featured\",\n            label: \"Featured\",\n            count: _data_mock_data__WEBPACK_IMPORTED_MODULE_2__.mockCasinoGames.filter((g)=>g.isFeatured).length\n        }\n    ];\n    const filteredGames = _data_mock_data__WEBPACK_IMPORTED_MODULE_2__.mockCasinoGames.filter((game)=>{\n        if (selectedCategory === \"all\") return true;\n        if (selectedCategory === \"slots\") return game.category === \"Slots\";\n        if (selectedCategory === \"live\") return game.isLive;\n        if (selectedCategory === \"featured\") return game.isFeatured;\n        return true;\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PlayIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        className: \"h-6 w-6 text-purple-400\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-white\",\n                        children: \"Casino Games\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-wrap gap-2\",\n                children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n                        whileHover: {\n                            scale: 1.05\n                        },\n                        whileTap: {\n                            scale: 0.95\n                        },\n                        onClick: ()=>setSelectedCategory(category.id),\n                        className: \"flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-200 \".concat(selectedCategory === category.id ? \"bg-purple-600 text-white\" : \"bg-dark-700 text-gray-300 hover:bg-dark-600 hover:text-white\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: category.label\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs bg-black/20 px-2 py-1 rounded-full\",\n                                children: category.count\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, category.id, true, {\n                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 2xl:grid-cols-8 gap-4 lg:gap-6 auto-rows-fr\",\n                children: filteredGames.map((game, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.4,\n                            delay: index * 0.05\n                        },\n                        whileHover: {\n                            scale: 1.05\n                        },\n                        className: \"group cursor-pointer\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative bg-dark-800 rounded-xl overflow-hidden border border-dark-700 hover:border-purple-500/50 transition-all duration-300\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"aspect-square bg-gradient-to-br from-purple-500/20 to-pink-500/20 flex items-center justify-center relative overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-full bg-gradient-to-br from-purple-600 to-pink-600 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PlayIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                className: \"h-12 w-12 text-white/80\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                                                lineNumber: 77,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-2 left-2 flex flex-col space-y-1\",\n                                            children: [\n                                                game.isNew && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"bg-green-500 text-white text-xs px-2 py-1 rounded-full font-medium\",\n                                                    children: \"NEW\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                                                    lineNumber: 83,\n                                                    columnNumber: 21\n                                                }, this),\n                                                game.isFeatured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"bg-yellow-500 text-black text-xs px-2 py-1 rounded-full font-medium\",\n                                                    children: \"⭐ FEATURED\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                                                    lineNumber: 88,\n                                                    columnNumber: 21\n                                                }, this),\n                                                game.isLive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"bg-red-500 text-white text-xs px-2 py-1 rounded-full font-medium flex items-center space-x-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-1 h-1 bg-white rounded-full animate-pulse\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                                                            lineNumber: 94,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"LIVE\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                                                            lineNumber: 95,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                                                    lineNumber: 93,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                initial: {\n                                                    scale: 0\n                                                },\n                                                whileHover: {\n                                                    scale: 1\n                                                },\n                                                className: \"w-16 h-16 bg-white rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PlayIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"h-8 w-8 text-purple-600 ml-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                                                    lineNumber: 107,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-2 right-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-black/70 text-white text-xs px-2 py-1 rounded-full\",\n                                                children: [\n                                                    \"RTP \",\n                                                    game.rtp,\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                                                lineNumber: 113,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-white font-medium text-sm mb-1 group-hover:text-purple-400 transition-colors\",\n                                            children: game.name\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400 text-xs mb-2\",\n                                            children: game.provider\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between text-xs\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-500\",\n                                                    children: [\n                                                        \"$\",\n                                                        game.minBet,\n                                                        \" - $\",\n                                                        game.maxBet\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                                                    lineNumber: 127,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PlayIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            className: \"h-3 w-3 text-yellow-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                                                            lineNumber: 131,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-400\",\n                                                            children: game.popularity\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                                                            lineNumber: 132,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                                                    lineNumber: 130,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 13\n                        }, this)\n                    }, game.id, false, {\n                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                lineNumber: 62,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n                    whileHover: {\n                        scale: 1.05\n                    },\n                    whileTap: {\n                        scale: 0.95\n                    },\n                    className: \"bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-8 py-3 rounded-xl font-semibold transition-all duration-200\",\n                    children: \"Load More Games\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                lineNumber: 142,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.6,\n                    delay: 0.3\n                },\n                className: \"bg-gradient-to-r from-yellow-500 via-orange-500 to-red-500 rounded-xl p-6 text-white relative overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-black/20\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative z-10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-bold mb-2\",\n                                            children: \"\\uD83C\\uDFB0 Progressive Jackpot\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-lg opacity-90\",\n                                            children: \"Current jackpot amount\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-right\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-4xl font-bold\",\n                                            children: \"$2,847,593\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm opacity-75\",\n                                            children: \"and growing...\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-4 right-4 w-16 h-16 bg-white/10 rounded-full animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-4 left-20 w-8 h-8 bg-yellow-400/20 rounded-full animate-bounce\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                lineNumber: 153,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n_s(CasinoLobby, \"ka1F1ceqEXioutdx48zEaS3nBME=\");\n_c = CasinoLobby;\nvar _c;\n$RefreshReg$(_c, \"CasinoLobby\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/casino/CasinoLobby.tsx\n"));

/***/ })

});