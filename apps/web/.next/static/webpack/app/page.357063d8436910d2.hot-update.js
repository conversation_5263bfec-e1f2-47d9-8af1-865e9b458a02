"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/StatsOverview.tsx":
/*!****************************************************!*\
  !*** ./src/components/dashboard/StatsOverview.tsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StatsOverview: function() { return /* binding */ StatsOverview; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/../../node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_CurrencyDollarIcon_FireIcon_TrophyIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=CurrencyDollarIcon,FireIcon,TrophyIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/../../node_modules/@heroicons/react/24/outline/esm/UsersIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CurrencyDollarIcon_FireIcon_TrophyIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CurrencyDollarIcon,FireIcon,TrophyIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/../../node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CurrencyDollarIcon_FireIcon_TrophyIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CurrencyDollarIcon,FireIcon,TrophyIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/../../node_modules/@heroicons/react/24/outline/esm/TrophyIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CurrencyDollarIcon_FireIcon_TrophyIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CurrencyDollarIcon,FireIcon,TrophyIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/../../node_modules/@heroicons/react/24/outline/esm/FireIcon.js\");\n/* harmony import */ var _data_mock_data__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/data/mock-data */ \"(app-pages-browser)/./src/data/mock-data.ts\");\n/* __next_internal_client_entry_do_not_use__ StatsOverview auto */ \n\n\n\nconst stats = [\n    {\n        id: \"users\",\n        label: \"Active Users\",\n        value: _data_mock_data__WEBPACK_IMPORTED_MODULE_1__.mockPlatformStats.totalUsers.toLocaleString(),\n        change: \"+12.5%\",\n        changeType: \"positive\",\n        icon: _barrel_optimize_names_CurrencyDollarIcon_FireIcon_TrophyIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        color: \"from-blue-500 to-blue-600\"\n    },\n    {\n        id: \"volume\",\n        label: \"Total Volume\",\n        value: \"$\".concat((_data_mock_data__WEBPACK_IMPORTED_MODULE_1__.mockPlatformStats.totalVolume / 1000000).toFixed(1), \"M\"),\n        change: \"+8.2%\",\n        changeType: \"positive\",\n        icon: _barrel_optimize_names_CurrencyDollarIcon_FireIcon_TrophyIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        color: \"from-green-500 to-green-600\"\n    },\n    {\n        id: \"bets\",\n        label: \"Total Bets\",\n        value: (_data_mock_data__WEBPACK_IMPORTED_MODULE_1__.mockPlatformStats.totalBets / 1000000).toFixed(1) + \"M\",\n        change: \"+15.3%\",\n        changeType: \"positive\",\n        icon: _barrel_optimize_names_CurrencyDollarIcon_FireIcon_TrophyIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        color: \"from-purple-500 to-purple-600\"\n    },\n    {\n        id: \"live\",\n        label: \"Live Events\",\n        value: _data_mock_data__WEBPACK_IMPORTED_MODULE_1__.mockPlatformStats.liveEvents.toString(),\n        change: \"+5\",\n        changeType: \"positive\",\n        icon: _barrel_optimize_names_CurrencyDollarIcon_FireIcon_TrophyIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        color: \"from-red-500 to-red-600\"\n    }\n];\nfunction StatsOverview() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 lg:gap-6 mb-6 lg:mb-8\",\n        children: [\n            stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.6,\n                        delay: index * 0.1\n                    },\n                    className: \"bg-dark-800 rounded-xl p-3 sm:p-4 lg:p-6 border border-dark-700 hover:border-dark-600 transition-colors\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 rounded-lg bg-gradient-to-r \".concat(stat.color),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                        className: \"h-6 w-6 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx\",\n                                        lineNumber: 64,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm font-medium \".concat(stat.changeType === \"positive\" ? \"text-green-400\" : \"text-red-400\"),\n                                    children: stat.change\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-2xl font-bold text-white mb-1\",\n                                    children: stat.value\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 text-sm\",\n                                    children: stat.label\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, stat.id, true, {\n                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 9\n                }, this)),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.6,\n                    delay: 0.4\n                },\n                className: \"md:col-span-2 xl:col-span-4 bg-dark-800 rounded-xl p-6 border border-dark-700\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-white flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CurrencyDollarIcon_FireIcon_TrophyIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-5 w-5 text-yellow-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Recent Big Winners\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-gray-400\",\n                                children: \"Live updates\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                        children: _data_mock_data__WEBPACK_IMPORTED_MODULE_1__.mockRecentWinners.map((winner, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    x: -20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                transition: {\n                                    duration: 0.4,\n                                    delay: 0.5 + index * 0.1\n                                },\n                                className: \"bg-dark-700 rounded-lg p-4 border border-dark-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-medium\",\n                                                children: winner.username\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-green-400 font-bold\",\n                                                children: [\n                                                    \"$\",\n                                                    winner.amount.toLocaleString()\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-400\",\n                                                children: winner.game\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-500\",\n                                                children: winner.time\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx\",\n        lineNumber: 53,\n        columnNumber: 5\n    }, this);\n}\n_c = StatsOverview;\nvar _c;\n$RefreshReg$(_c, \"StatsOverview\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/StatsOverview.tsx\n"));

/***/ })

});