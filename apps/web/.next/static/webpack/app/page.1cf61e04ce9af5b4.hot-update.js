"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/data/mock-data.ts":
/*!*******************************!*\
  !*** ./src/data/mock-data.ts ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mockCasinoGames: function() { return /* binding */ mockCasinoGames; },\n/* harmony export */   mockEvents: function() { return /* binding */ mockEvents; },\n/* harmony export */   mockPlatformStats: function() { return /* binding */ mockPlatformStats; },\n/* harmony export */   mockRecentWinners: function() { return /* binding */ mockRecentWinners; },\n/* harmony export */   mockSports: function() { return /* binding */ mockSports; },\n/* harmony export */   mockTrendingBets: function() { return /* binding */ mockTrendingBets; },\n/* harmony export */   mockUserStats: function() { return /* binding */ mockUserStats; }\n/* harmony export */ });\n// Mock Sports Data\nconst mockSports = [\n    {\n        id: \"1\",\n        name: \"Football\",\n        slug: \"football\",\n        icon: \"⚽\",\n        isActive: true,\n        eventCount: 156\n    },\n    {\n        id: \"2\",\n        name: \"Basketball\",\n        slug: \"basketball\",\n        icon: \"\\uD83C\\uDFC0\",\n        isActive: true,\n        eventCount: 89\n    },\n    {\n        id: \"3\",\n        name: \"Tennis\",\n        slug: \"tennis\",\n        icon: \"\\uD83C\\uDFBE\",\n        isActive: true,\n        eventCount: 234\n    },\n    {\n        id: \"4\",\n        name: \"Cricket\",\n        slug: \"cricket\",\n        icon: \"\\uD83C\\uDFCF\",\n        isActive: true,\n        eventCount: 45\n    },\n    {\n        id: \"5\",\n        name: \"Ice Hockey\",\n        slug: \"ice-hockey\",\n        icon: \"\\uD83C\\uDFD2\",\n        isActive: true,\n        eventCount: 67\n    },\n    {\n        id: \"6\",\n        name: \"Baseball\",\n        slug: \"baseball\",\n        icon: \"⚾\",\n        isActive: true,\n        eventCount: 123\n    }\n];\n// Mock Events Data\nconst mockEvents = [\n    {\n        id: \"1\",\n        leagueId: \"1\",\n        homeTeam: {\n            id: \"1\",\n            name: \"Manchester United\",\n            logo: \"/teams/man-utd.png\"\n        },\n        awayTeam: {\n            id: \"2\",\n            name: \"Liverpool\",\n            logo: \"/teams/liverpool.png\"\n        },\n        startTime: \"2024-01-15T20:00:00.000Z\",\n        status: \"scheduled\",\n        isLive: false,\n        markets: [\n            {\n                id: \"1\",\n                eventId: \"1\",\n                name: \"Match Winner\",\n                type: \"match_winner\",\n                isActive: true,\n                isSuspended: false,\n                outcomes: [\n                    {\n                        id: \"1\",\n                        marketId: \"1\",\n                        name: \"Manchester United\",\n                        odds: 2.45,\n                        isActive: true,\n                        trend: \"up\"\n                    },\n                    {\n                        id: \"2\",\n                        marketId: \"1\",\n                        name: \"Draw\",\n                        odds: 3.20,\n                        isActive: true,\n                        trend: \"neutral\"\n                    },\n                    {\n                        id: \"3\",\n                        marketId: \"1\",\n                        name: \"Liverpool\",\n                        odds: 2.80,\n                        isActive: true,\n                        trend: \"down\"\n                    }\n                ]\n            },\n            {\n                id: \"2\",\n                eventId: \"1\",\n                name: \"Total Goals\",\n                type: \"over_under\",\n                isActive: true,\n                isSuspended: false,\n                outcomes: [\n                    {\n                        id: \"4\",\n                        marketId: \"2\",\n                        name: \"Over 2.5\",\n                        odds: 1.85,\n                        isActive: true,\n                        trend: \"up\"\n                    },\n                    {\n                        id: \"5\",\n                        marketId: \"2\",\n                        name: \"Under 2.5\",\n                        odds: 1.95,\n                        isActive: true,\n                        trend: \"down\"\n                    }\n                ]\n            }\n        ]\n    },\n    {\n        id: \"2\",\n        leagueId: \"1\",\n        homeTeam: {\n            id: \"3\",\n            name: \"Chelsea\",\n            logo: \"/teams/chelsea.png\"\n        },\n        awayTeam: {\n            id: \"4\",\n            name: \"Arsenal\",\n            logo: \"/teams/arsenal.png\"\n        },\n        startTime: \"2024-01-15T18:00:00.000Z\",\n        status: \"live\",\n        isLive: true,\n        homeScore: 1,\n        awayScore: 2,\n        minute: 67,\n        period: \"2nd Half\",\n        markets: [\n            {\n                id: \"3\",\n                eventId: \"2\",\n                name: \"Match Winner\",\n                type: \"match_winner\",\n                isActive: true,\n                isSuspended: false,\n                outcomes: [\n                    {\n                        id: \"6\",\n                        marketId: \"3\",\n                        name: \"Chelsea\",\n                        odds: 4.50,\n                        isActive: true,\n                        trend: \"up\"\n                    },\n                    {\n                        id: \"7\",\n                        marketId: \"3\",\n                        name: \"Draw\",\n                        odds: 3.80,\n                        isActive: true,\n                        trend: \"neutral\"\n                    },\n                    {\n                        id: \"8\",\n                        marketId: \"3\",\n                        name: \"Arsenal\",\n                        odds: 1.65,\n                        isActive: true,\n                        trend: \"down\"\n                    }\n                ]\n            }\n        ]\n    },\n    {\n        id: \"3\",\n        leagueId: \"2\",\n        homeTeam: {\n            id: \"5\",\n            name: \"Lakers\",\n            logo: \"/teams/lakers.png\"\n        },\n        awayTeam: {\n            id: \"6\",\n            name: \"Warriors\",\n            logo: \"/teams/warriors.png\"\n        },\n        startTime: new Date(Date.now() + 4 * 60 * 60 * 1000).toISOString(),\n        status: \"scheduled\",\n        isLive: false,\n        markets: [\n            {\n                id: \"4\",\n                eventId: \"3\",\n                name: \"Match Winner\",\n                type: \"match_winner\",\n                isActive: true,\n                isSuspended: false,\n                outcomes: [\n                    {\n                        id: \"9\",\n                        marketId: \"4\",\n                        name: \"Lakers\",\n                        odds: 1.95,\n                        isActive: true,\n                        trend: \"neutral\"\n                    },\n                    {\n                        id: \"10\",\n                        marketId: \"4\",\n                        name: \"Warriors\",\n                        odds: 1.85,\n                        isActive: true,\n                        trend: \"up\"\n                    }\n                ]\n            }\n        ]\n    }\n];\n// Mock Casino Games\nconst mockCasinoGames = [\n    {\n        id: \"1\",\n        name: \"Mega Moolah\",\n        provider: \"Microgaming\",\n        category: \"Slots\",\n        thumbnail: \"/games/mega-moolah.jpg\",\n        isLive: false,\n        minBet: 0.25,\n        maxBet: 6.25,\n        rtp: 88.12,\n        popularity: 95,\n        isNew: false,\n        isFeatured: true\n    },\n    {\n        id: \"2\",\n        name: \"Live Blackjack\",\n        provider: \"Evolution Gaming\",\n        category: \"Live Casino\",\n        thumbnail: \"/games/live-blackjack.jpg\",\n        isLive: true,\n        minBet: 5,\n        maxBet: 5000,\n        rtp: 99.28,\n        popularity: 88,\n        isNew: false,\n        isFeatured: true\n    },\n    {\n        id: \"3\",\n        name: \"Starburst\",\n        provider: \"NetEnt\",\n        category: \"Slots\",\n        thumbnail: \"/games/starburst.jpg\",\n        isLive: false,\n        minBet: 0.10,\n        maxBet: 100,\n        rtp: 96.09,\n        popularity: 92,\n        isNew: false,\n        isFeatured: false\n    },\n    {\n        id: \"4\",\n        name: \"Lightning Roulette\",\n        provider: \"Evolution Gaming\",\n        category: \"Live Casino\",\n        thumbnail: \"/games/lightning-roulette.jpg\",\n        isLive: true,\n        minBet: 0.20,\n        maxBet: 20000,\n        rtp: 97.30,\n        popularity: 90,\n        isNew: true,\n        isFeatured: true\n    }\n];\n// Mock User Stats\nconst mockUserStats = {\n    totalBets: 1247,\n    totalWins: 623,\n    totalLosses: 624,\n    winRate: 49.96,\n    totalStaked: 12450.00,\n    totalWon: 13890.50,\n    netProfit: 1440.50,\n    biggestWin: 2850.00,\n    currentStreak: 3,\n    longestStreak: 12\n};\n// Mock Platform Stats\nconst mockPlatformStats = {\n    totalUsers: 125847,\n    totalBets: 2847593,\n    totalVolume: 45892347.50,\n    liveEvents: 156,\n    popularSports: mockSports.slice(0, 3),\n    biggestWins: [\n        {\n            id: \"1\",\n            userId: \"user1\",\n            type: \"combo\",\n            selections: [],\n            stake: 50,\n            potentialWin: 15750,\n            totalOdds: 315,\n            status: \"won\",\n            placedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),\n            settledAt: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString()\n        },\n        {\n            id: \"2\",\n            userId: \"user2\",\n            type: \"single\",\n            selections: [],\n            stake: 1000,\n            potentialWin: 8500,\n            totalOdds: 8.5,\n            status: \"won\",\n            placedAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),\n            settledAt: new Date(Date.now() - 5 * 60 * 60 * 1000).toISOString()\n        }\n    ]\n};\n// Mock trending bets\nconst mockTrendingBets = [\n    {\n        event: \"Manchester United vs Liverpool\",\n        selection: \"Over 2.5 Goals\",\n        odds: 1.85,\n        percentage: 78\n    },\n    {\n        event: \"Lakers vs Warriors\",\n        selection: \"Lakers to Win\",\n        odds: 1.95,\n        percentage: 65\n    },\n    {\n        event: \"Chelsea vs Arsenal\",\n        selection: \"Arsenal to Win\",\n        odds: 1.65,\n        percentage: 82\n    }\n];\n// Mock recent winners\nconst mockRecentWinners = [\n    {\n        username: \"BetMaster2023\",\n        amount: 15750,\n        game: \"Football Combo\",\n        time: \"2 minutes ago\"\n    },\n    {\n        username: \"LuckyPlayer\",\n        amount: 8500,\n        game: \"Live Blackjack\",\n        time: \"5 minutes ago\"\n    },\n    {\n        username: \"SportsFan\",\n        amount: 3200,\n        game: \"Basketball\",\n        time: \"8 minutes ago\"\n    }\n];\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/data/mock-data.ts\n"));

/***/ })

});