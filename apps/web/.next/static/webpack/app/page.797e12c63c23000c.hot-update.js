"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/sports/LiveEvents.tsx":
/*!**********************************************!*\
  !*** ./src/components/sports/LiveEvents.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LiveEvents: function() { return /* binding */ LiveEvents; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/../../node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_ClockIcon_FireIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ClockIcon,FireIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/../../node_modules/@heroicons/react/24/outline/esm/FireIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ClockIcon_FireIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ClockIcon,FireIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/../../node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _data_mock_data__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/data/mock-data */ \"(app-pages-browser)/./src/data/mock-data.ts\");\n/* harmony import */ var _store_betting_store__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/betting-store */ \"(app-pages-browser)/./src/store/betting-store.tsx\");\n/* __next_internal_client_entry_do_not_use__ LiveEvents auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction LiveEvents() {\n    _s();\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { addSelection } = (0,_store_betting_store__WEBPACK_IMPORTED_MODULE_3__.useBetting)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setMounted(true);\n    }, []);\n    const liveEvents = _data_mock_data__WEBPACK_IMPORTED_MODULE_2__.mockEvents.filter((event)=>event.isLive);\n    const handleOddsClick = (event, market, outcome)=>{\n        const selection = {\n            outcomeId: outcome.id,\n            eventId: event.id,\n            marketId: market.id,\n            eventName: \"\".concat(event.homeTeam.name, \" vs \").concat(event.awayTeam.name),\n            marketName: market.name,\n            outcomeName: outcome.name,\n            odds: outcome.odds,\n            isLive: event.isLive\n        };\n        addSelection(selection);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"betting-section-spacing\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-3 h-3 bg-red-500 rounded-full animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                                lineNumber: 39,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_FireIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"h-6 w-6 text-red-400\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-white\",\n                        children: \"Live Events\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"bg-red-500 text-white text-sm px-3 py-1 rounded-full\",\n                        children: [\n                            liveEvents.length,\n                            \" Live\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: liveEvents.map((event, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            scale: 0.95\n                        },\n                        animate: {\n                            opacity: 1,\n                            scale: 1\n                        },\n                        transition: {\n                            duration: 0.4,\n                            delay: index * 0.1\n                        },\n                        className: \"bg-gradient-to-br from-dark-800 to-dark-900 rounded-xl border border-red-500/30 overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-red-500/10 border-b border-red-500/30 p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-red-500 rounded-full animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                                                    lineNumber: 62,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-400 font-semibold text-sm\",\n                                                    children: \"LIVE\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                                                    lineNumber: 63,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-white font-bold\",\n                                            children: [\n                                                event.minute,\n                                                \"' \",\n                                                event.period\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                                            lineNumber: 65,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                                lineNumber: 59,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-10 h-10 bg-dark-600 rounded-full flex items-center justify-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-bold text-white\",\n                                                                        children: event.homeTeam.name.charAt(0)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                                                                        lineNumber: 79,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                                                                    lineNumber: 78,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-white font-medium\",\n                                                                    children: event.homeTeam.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                                                                    lineNumber: 83,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                                                            lineNumber: 77,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-3xl font-bold text-white\",\n                                                            children: event.homeScore\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                                                            lineNumber: 85,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                                                    lineNumber: 76,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-10 h-10 bg-dark-600 rounded-full flex items-center justify-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-bold text-white\",\n                                                                        children: event.awayTeam.name.charAt(0)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                                                                        lineNumber: 94,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                                                                    lineNumber: 93,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-white font-medium\",\n                                                                    children: event.awayTeam.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                                                                    lineNumber: 98,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                                                            lineNumber: 92,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-3xl font-bold text-white\",\n                                                            children: event.awayScore\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                                                            lineNumber: 100,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                                                    lineNumber: 91,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                                            lineNumber: 74,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: event.markets.map((market)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-gray-400 text-sm font-medium mb-3\",\n                                                        children: market.name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                                                        lineNumber: 111,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-3 gap-2\",\n                                                        children: market.outcomes.map((outcome)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                                                                whileHover: {\n                                                                    scale: 1.05\n                                                                },\n                                                                whileTap: {\n                                                                    scale: 0.95\n                                                                },\n                                                                onClick: ()=>handleOddsClick(event, market, outcome),\n                                                                className: \"bg-dark-700 hover:bg-dark-600 border border-dark-600 hover:border-primary-500 rounded-lg p-3 transition-all duration-200 group\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-white font-medium text-sm mb-1 group-hover:text-primary-400\",\n                                                                        children: outcome.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                                                                        lineNumber: 123,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-primary-400 font-bold text-lg\",\n                                                                                children: outcome.odds.toFixed(2)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                                                                                lineNumber: 127,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            outcome.trend && outcome.trend !== \"neutral\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.span, {\n                                                                                initial: {\n                                                                                    scale: 0\n                                                                                },\n                                                                                animate: {\n                                                                                    scale: 1\n                                                                                },\n                                                                                className: \"text-sm \".concat(outcome.trend === \"up\" ? \"text-green-400\" : \"text-red-400\"),\n                                                                                children: outcome.trend === \"up\" ? \"↗\" : \"↘\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                                                                                lineNumber: 131,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                                                                        lineNumber: 126,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, outcome.id, true, {\n                                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                                                                lineNumber: 116,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                                                        lineNumber: 114,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, market.id, true, {\n                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                                                lineNumber: 110,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, event.id, true, {\n                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, this),\n            liveEvents.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_FireIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"h-16 w-16 text-gray-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-semibold text-white mb-2\",\n                        children: \"No Live Events\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400\",\n                        children: \"Check back soon for live betting opportunities\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                lineNumber: 154,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, this);\n}\n_s(LiveEvents, \"rh3MfYZLpGxPC/PYSn3SF0IdCsw=\", false, function() {\n    return [\n        _store_betting_store__WEBPACK_IMPORTED_MODULE_3__.useBetting\n    ];\n});\n_c = LiveEvents;\nvar _c;\n$RefreshReg$(_c, \"LiveEvents\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3Nwb3J0cy9MaXZlRXZlbnRzLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQUUyQztBQUNMO0FBQzJCO0FBQ3BCO0FBQ0s7QUFHM0MsU0FBU087O0lBQ2QsTUFBTSxDQUFDQyxTQUFTQyxXQUFXLEdBQUdULCtDQUFRQSxDQUFDO0lBQ3ZDLE1BQU0sRUFBRVUsWUFBWSxFQUFFLEdBQUdKLGdFQUFVQTtJQUVuQ0wsZ0RBQVNBLENBQUM7UUFDUlEsV0FBVztJQUNiLEdBQUcsRUFBRTtJQUVMLE1BQU1FLGFBQWFOLHVEQUFVQSxDQUFDTyxNQUFNLENBQUNDLENBQUFBLFFBQVNBLE1BQU1DLE1BQU07SUFFMUQsTUFBTUMsa0JBQWtCLENBQUNGLE9BQVlHLFFBQWFDO1FBQ2hELE1BQU1DLFlBQTBCO1lBQzlCQyxXQUFXRixRQUFRRyxFQUFFO1lBQ3JCQyxTQUFTUixNQUFNTyxFQUFFO1lBQ2pCRSxVQUFVTixPQUFPSSxFQUFFO1lBQ25CRyxXQUFXLEdBQTZCVixPQUExQkEsTUFBTVcsUUFBUSxDQUFDQyxJQUFJLEVBQUMsUUFBMEIsT0FBcEJaLE1BQU1hLFFBQVEsQ0FBQ0QsSUFBSTtZQUMzREUsWUFBWVgsT0FBT1MsSUFBSTtZQUN2QkcsYUFBYVgsUUFBUVEsSUFBSTtZQUN6QkksTUFBTVosUUFBUVksSUFBSTtZQUNsQmYsUUFBUUQsTUFBTUMsTUFBTTtRQUN0QjtRQUNBSixhQUFhUTtJQUNmO0lBRUEscUJBQ0UsOERBQUNZO1FBQUlDLFdBQVU7OzBCQUViLDhEQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7Ozs7OzswQ0FDZiw4REFBQzVCLDRHQUFRQTtnQ0FBQzRCLFdBQVU7Ozs7Ozs7Ozs7OztrQ0FFdEIsOERBQUNDO3dCQUFHRCxXQUFVO2tDQUFnQzs7Ozs7O2tDQUM5Qyw4REFBQ0U7d0JBQUtGLFdBQVU7OzRCQUNicEIsV0FBV3VCLE1BQU07NEJBQUM7Ozs7Ozs7Ozs7Ozs7MEJBS3ZCLDhEQUFDSjtnQkFBSUMsV0FBVTswQkFDWnBCLFdBQVd3QixHQUFHLENBQUMsQ0FBQ3RCLE9BQU91QixzQkFDdEIsOERBQUNsQyxpREFBTUEsQ0FBQzRCLEdBQUc7d0JBRVRPLFNBQVM7NEJBQUVDLFNBQVM7NEJBQUdDLE9BQU87d0JBQUs7d0JBQ25DQyxTQUFTOzRCQUFFRixTQUFTOzRCQUFHQyxPQUFPO3dCQUFFO3dCQUNoQ0UsWUFBWTs0QkFBRUMsVUFBVTs0QkFBS0MsT0FBT1AsUUFBUTt3QkFBSTt3QkFDaERMLFdBQVU7OzBDQUdWLDhEQUFDRDtnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNEO29EQUFJQyxXQUFVOzs7Ozs7OERBQ2YsOERBQUNFO29EQUFLRixXQUFVOzhEQUFxQzs7Ozs7Ozs7Ozs7O3NEQUV2RCw4REFBQ0Q7NENBQUlDLFdBQVU7O2dEQUNabEIsTUFBTStCLE1BQU07Z0RBQUM7Z0RBQUcvQixNQUFNZ0MsTUFBTTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQU1uQyw4REFBQ2Y7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVTtrREFDYiw0RUFBQ0Q7NENBQUlDLFdBQVU7OzhEQUViLDhEQUFDRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNEOzREQUFJQyxXQUFVOzs4RUFDYiw4REFBQ0Q7b0VBQUlDLFdBQVU7OEVBQ2IsNEVBQUNFO3dFQUFLRixXQUFVO2tGQUNibEIsTUFBTVcsUUFBUSxDQUFDQyxJQUFJLENBQUNxQixNQUFNLENBQUM7Ozs7Ozs7Ozs7OzhFQUdoQyw4REFBQ2I7b0VBQUtGLFdBQVU7OEVBQTBCbEIsTUFBTVcsUUFBUSxDQUFDQyxJQUFJOzs7Ozs7Ozs7Ozs7c0VBRS9ELDhEQUFDUTs0REFBS0YsV0FBVTtzRUFDYmxCLE1BQU1rQyxTQUFTOzs7Ozs7Ozs7Ozs7OERBS3BCLDhEQUFDakI7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDRDs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUNEO29FQUFJQyxXQUFVOzhFQUNiLDRFQUFDRTt3RUFBS0YsV0FBVTtrRkFDYmxCLE1BQU1hLFFBQVEsQ0FBQ0QsSUFBSSxDQUFDcUIsTUFBTSxDQUFDOzs7Ozs7Ozs7Ozs4RUFHaEMsOERBQUNiO29FQUFLRixXQUFVOzhFQUEwQmxCLE1BQU1hLFFBQVEsQ0FBQ0QsSUFBSTs7Ozs7Ozs7Ozs7O3NFQUUvRCw4REFBQ1E7NERBQUtGLFdBQVU7c0VBQ2JsQixNQUFNbUMsU0FBUzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBT3hCLDhEQUFDbEI7d0NBQUlDLFdBQVU7a0RBQ1psQixNQUFNb0MsT0FBTyxDQUFDZCxHQUFHLENBQUMsQ0FBQ25CLHVCQUNsQiw4REFBQ2M7O2tFQUNDLDhEQUFDb0I7d0RBQUduQixXQUFVO2tFQUNYZixPQUFPUyxJQUFJOzs7Ozs7a0VBRWQsOERBQUNLO3dEQUFJQyxXQUFVO2tFQUNaZixPQUFPbUMsUUFBUSxDQUFDaEIsR0FBRyxDQUFDLENBQUNsQix3QkFDcEIsOERBQUNmLGlEQUFNQSxDQUFDa0QsTUFBTTtnRUFFWkMsWUFBWTtvRUFBRWQsT0FBTztnRUFBSztnRUFDMUJlLFVBQVU7b0VBQUVmLE9BQU87Z0VBQUs7Z0VBQ3hCZ0IsU0FBUyxJQUFNeEMsZ0JBQWdCRixPQUFPRyxRQUFRQztnRUFDOUNjLFdBQVU7O2tGQUVWLDhEQUFDRDt3RUFBSUMsV0FBVTtrRkFDWmQsUUFBUVEsSUFBSTs7Ozs7O2tGQUVmLDhEQUFDSzt3RUFBSUMsV0FBVTs7MEZBQ2IsOERBQUNFO2dGQUFLRixXQUFVOzBGQUNiZCxRQUFRWSxJQUFJLENBQUMyQixPQUFPLENBQUM7Ozs7Ozs0RUFFdkJ2QyxRQUFRd0MsS0FBSyxJQUFJeEMsUUFBUXdDLEtBQUssS0FBSywyQkFDbEMsOERBQUN2RCxpREFBTUEsQ0FBQytCLElBQUk7Z0ZBQ1ZJLFNBQVM7b0ZBQUVFLE9BQU87Z0ZBQUU7Z0ZBQ3BCQyxTQUFTO29GQUFFRCxPQUFPO2dGQUFFO2dGQUNwQlIsV0FBVyxXQUVWLE9BRENkLFFBQVF3QyxLQUFLLEtBQUssT0FBTyxtQkFBbUI7MEZBRzdDeEMsUUFBUXdDLEtBQUssS0FBSyxPQUFPLE1BQU07Ozs7Ozs7Ozs7Ozs7K0RBckJqQ3hDLFFBQVFHLEVBQUU7Ozs7Ozs7Ozs7OytDQVBiSixPQUFPSSxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozt1QkExRHBCUCxNQUFNTyxFQUFFOzs7Ozs7Ozs7O1lBcUdsQlQsV0FBV3VCLE1BQU0sS0FBSyxtQkFDckIsOERBQUNKO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQzNCLDRHQUFTQTt3QkFBQzJCLFdBQVU7Ozs7OztrQ0FDckIsOERBQUMyQjt3QkFBRzNCLFdBQVU7a0NBQXdDOzs7Ozs7a0NBQ3RELDhEQUFDNEI7d0JBQUU1QixXQUFVO2tDQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBT3ZDO0dBMUpnQnhCOztRQUVXRCw0REFBVUE7OztLQUZyQkMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbXBvbmVudHMvc3BvcnRzL0xpdmVFdmVudHMudHN4PzE0ZGEiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7IG1vdGlvbiB9IGZyb20gJ2ZyYW1lci1tb3Rpb24nXG5pbXBvcnQgeyBGaXJlSWNvbiwgQ2xvY2tJY29uIH0gZnJvbSAnQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lJ1xuaW1wb3J0IHsgbW9ja0V2ZW50cyB9IGZyb20gJ0AvZGF0YS9tb2NrLWRhdGEnXG5pbXBvcnQgeyB1c2VCZXR0aW5nIH0gZnJvbSAnQC9zdG9yZS9iZXR0aW5nLXN0b3JlJ1xuaW1wb3J0IHsgQmV0U2VsZWN0aW9uIH0gZnJvbSAnQC90eXBlcydcblxuZXhwb3J0IGZ1bmN0aW9uIExpdmVFdmVudHMoKSB7XG4gIGNvbnN0IFttb3VudGVkLCBzZXRNb3VudGVkXSA9IHVzZVN0YXRlKGZhbHNlKVxuICBjb25zdCB7IGFkZFNlbGVjdGlvbiB9ID0gdXNlQmV0dGluZygpXG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBzZXRNb3VudGVkKHRydWUpXG4gIH0sIFtdKVxuXG4gIGNvbnN0IGxpdmVFdmVudHMgPSBtb2NrRXZlbnRzLmZpbHRlcihldmVudCA9PiBldmVudC5pc0xpdmUpXG5cbiAgY29uc3QgaGFuZGxlT2Rkc0NsaWNrID0gKGV2ZW50OiBhbnksIG1hcmtldDogYW55LCBvdXRjb21lOiBhbnkpID0+IHtcbiAgICBjb25zdCBzZWxlY3Rpb246IEJldFNlbGVjdGlvbiA9IHtcbiAgICAgIG91dGNvbWVJZDogb3V0Y29tZS5pZCxcbiAgICAgIGV2ZW50SWQ6IGV2ZW50LmlkLFxuICAgICAgbWFya2V0SWQ6IG1hcmtldC5pZCxcbiAgICAgIGV2ZW50TmFtZTogYCR7ZXZlbnQuaG9tZVRlYW0ubmFtZX0gdnMgJHtldmVudC5hd2F5VGVhbS5uYW1lfWAsXG4gICAgICBtYXJrZXROYW1lOiBtYXJrZXQubmFtZSxcbiAgICAgIG91dGNvbWVOYW1lOiBvdXRjb21lLm5hbWUsXG4gICAgICBvZGRzOiBvdXRjb21lLm9kZHMsXG4gICAgICBpc0xpdmU6IGV2ZW50LmlzTGl2ZSxcbiAgICB9XG4gICAgYWRkU2VsZWN0aW9uKHNlbGVjdGlvbilcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJiZXR0aW5nLXNlY3Rpb24tc3BhY2luZ1wiPlxuICAgICAgey8qIEhlYWRlciAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTMgaC0zIGJnLXJlZC01MDAgcm91bmRlZC1mdWxsIGFuaW1hdGUtcHVsc2VcIj48L2Rpdj5cbiAgICAgICAgICA8RmlyZUljb24gY2xhc3NOYW1lPVwiaC02IHctNiB0ZXh0LXJlZC00MDBcIiAvPlxuICAgICAgICA8L2Rpdj5cbiAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LXdoaXRlXCI+TGl2ZSBFdmVudHM8L2gyPlxuICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJiZy1yZWQtNTAwIHRleHQtd2hpdGUgdGV4dC1zbSBweC0zIHB5LTEgcm91bmRlZC1mdWxsXCI+XG4gICAgICAgICAge2xpdmVFdmVudHMubGVuZ3RofSBMaXZlXG4gICAgICAgIDwvc3Bhbj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogTGl2ZSBFdmVudHMgR3JpZCAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBsZzpncmlkLWNvbHMtMiBnYXAtNlwiPlxuICAgICAgICB7bGl2ZUV2ZW50cy5tYXAoKGV2ZW50LCBpbmRleCkgPT4gKFxuICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICBrZXk9e2V2ZW50LmlkfVxuICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCBzY2FsZTogMC45NSB9fVxuICAgICAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCBzY2FsZTogMSB9fVxuICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC40LCBkZWxheTogaW5kZXggKiAwLjEgfX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWdyYWRpZW50LXRvLWJyIGZyb20tZGFyay04MDAgdG8tZGFyay05MDAgcm91bmRlZC14bCBib3JkZXIgYm9yZGVyLXJlZC01MDAvMzAgb3ZlcmZsb3ctaGlkZGVuXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICB7LyogTGl2ZSBIZWFkZXIgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXJlZC01MDAvMTAgYm9yZGVyLWIgYm9yZGVyLXJlZC01MDAvMzAgcC00XCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0yIGgtMiBiZy1yZWQtNTAwIHJvdW5kZWQtZnVsbCBhbmltYXRlLXB1bHNlXCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXJlZC00MDAgZm9udC1zZW1pYm9sZCB0ZXh0LXNtXCI+TElWRTwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtd2hpdGUgZm9udC1ib2xkXCI+XG4gICAgICAgICAgICAgICAgICB7ZXZlbnQubWludXRlfScge2V2ZW50LnBlcmlvZH1cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIE1hdGNoIEluZm8gKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNFwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBtYi02XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgc3BhY2UteS0zXCI+XG4gICAgICAgICAgICAgICAgICB7LyogSG9tZSBUZWFtICovfVxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTNcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTAgaC0xMCBiZy1kYXJrLTYwMCByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1ib2xkIHRleHQtd2hpdGVcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge2V2ZW50LmhvbWVUZWFtLm5hbWUuY2hhckF0KDApfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtd2hpdGUgZm9udC1tZWRpdW1cIj57ZXZlbnQuaG9tZVRlYW0ubmFtZX08L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBmb250LWJvbGQgdGV4dC13aGl0ZVwiPlxuICAgICAgICAgICAgICAgICAgICAgIHtldmVudC5ob21lU2NvcmV9XG4gICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICB7LyogQXdheSBUZWFtICovfVxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTNcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTAgaC0xMCBiZy1kYXJrLTYwMCByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1ib2xkIHRleHQtd2hpdGVcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge2V2ZW50LmF3YXlUZWFtLm5hbWUuY2hhckF0KDApfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtd2hpdGUgZm9udC1tZWRpdW1cIj57ZXZlbnQuYXdheVRlYW0ubmFtZX08L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBmb250LWJvbGQgdGV4dC13aGl0ZVwiPlxuICAgICAgICAgICAgICAgICAgICAgIHtldmVudC5hd2F5U2NvcmV9XG4gICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICB7LyogTGl2ZSBNYXJrZXRzICovfVxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICAgIHtldmVudC5tYXJrZXRzLm1hcCgobWFya2V0KSA9PiAoXG4gICAgICAgICAgICAgICAgICA8ZGl2IGtleT17bWFya2V0LmlkfT5cbiAgICAgICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDAgdGV4dC1zbSBmb250LW1lZGl1bSBtYi0zXCI+XG4gICAgICAgICAgICAgICAgICAgICAge21hcmtldC5uYW1lfVxuICAgICAgICAgICAgICAgICAgICA8L2g0PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTMgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICB7bWFya2V0Lm91dGNvbWVzLm1hcCgob3V0Y29tZSkgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgPG1vdGlvbi5idXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAga2V5PXtvdXRjb21lLmlkfVxuICAgICAgICAgICAgICAgICAgICAgICAgICB3aGlsZUhvdmVyPXt7IHNjYWxlOiAxLjA1IH19XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHdoaWxlVGFwPXt7IHNjYWxlOiAwLjk1IH19XG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZU9kZHNDbGljayhldmVudCwgbWFya2V0LCBvdXRjb21lKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctZGFyay03MDAgaG92ZXI6YmctZGFyay02MDAgYm9yZGVyIGJvcmRlci1kYXJrLTYwMCBob3Zlcjpib3JkZXItcHJpbWFyeS01MDAgcm91bmRlZC1sZyBwLTMgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwIGdyb3VwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIGZvbnQtbWVkaXVtIHRleHQtc20gbWItMSBncm91cC1ob3Zlcjp0ZXh0LXByaW1hcnktNDAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge291dGNvbWUubmFtZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1wcmltYXJ5LTQwMCBmb250LWJvbGQgdGV4dC1sZ1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge291dGNvbWUub2Rkcy50b0ZpeGVkKDIpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7b3V0Y29tZS50cmVuZCAmJiBvdXRjb21lLnRyZW5kICE9PSAnbmV1dHJhbCcgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPG1vdGlvbi5zcGFuXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGluaXRpYWw9e3sgc2NhbGU6IDAgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYW5pbWF0ZT17eyBzY2FsZTogMSB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2B0ZXh0LXNtICR7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb3V0Y29tZS50cmVuZCA9PT0gJ3VwJyA/ICd0ZXh0LWdyZWVuLTQwMCcgOiAndGV4dC1yZWQtNDAwJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge291dGNvbWUudHJlbmQgPT09ICd1cCcgPyAn4oaXJyA6ICfihpgnfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9tb3Rpb24uc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvbW90aW9uLmJ1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L21vdGlvbi5kaXY+XG4gICAgICAgICkpfVxuICAgICAgPC9kaXY+XG5cbiAgICAgIHtsaXZlRXZlbnRzLmxlbmd0aCA9PT0gMCAmJiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcHktMTJcIj5cbiAgICAgICAgICA8Q2xvY2tJY29uIGNsYXNzTmFtZT1cImgtMTYgdy0xNiB0ZXh0LWdyYXktNjAwIG14LWF1dG8gbWItNFwiIC8+XG4gICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1zZW1pYm9sZCB0ZXh0LXdoaXRlIG1iLTJcIj5ObyBMaXZlIEV2ZW50czwvaDM+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMFwiPlxuICAgICAgICAgICAgQ2hlY2sgYmFjayBzb29uIGZvciBsaXZlIGJldHRpbmcgb3Bwb3J0dW5pdGllc1xuICAgICAgICAgIDwvcD5cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJtb3Rpb24iLCJGaXJlSWNvbiIsIkNsb2NrSWNvbiIsIm1vY2tFdmVudHMiLCJ1c2VCZXR0aW5nIiwiTGl2ZUV2ZW50cyIsIm1vdW50ZWQiLCJzZXRNb3VudGVkIiwiYWRkU2VsZWN0aW9uIiwibGl2ZUV2ZW50cyIsImZpbHRlciIsImV2ZW50IiwiaXNMaXZlIiwiaGFuZGxlT2Rkc0NsaWNrIiwibWFya2V0Iiwib3V0Y29tZSIsInNlbGVjdGlvbiIsIm91dGNvbWVJZCIsImlkIiwiZXZlbnRJZCIsIm1hcmtldElkIiwiZXZlbnROYW1lIiwiaG9tZVRlYW0iLCJuYW1lIiwiYXdheVRlYW0iLCJtYXJrZXROYW1lIiwib3V0Y29tZU5hbWUiLCJvZGRzIiwiZGl2IiwiY2xhc3NOYW1lIiwiaDIiLCJzcGFuIiwibGVuZ3RoIiwibWFwIiwiaW5kZXgiLCJpbml0aWFsIiwib3BhY2l0eSIsInNjYWxlIiwiYW5pbWF0ZSIsInRyYW5zaXRpb24iLCJkdXJhdGlvbiIsImRlbGF5IiwibWludXRlIiwicGVyaW9kIiwiY2hhckF0IiwiaG9tZVNjb3JlIiwiYXdheVNjb3JlIiwibWFya2V0cyIsImg0Iiwib3V0Y29tZXMiLCJidXR0b24iLCJ3aGlsZUhvdmVyIiwid2hpbGVUYXAiLCJvbkNsaWNrIiwidG9GaXhlZCIsInRyZW5kIiwiaDMiLCJwIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sports/LiveEvents.tsx\n"));

/***/ })

});