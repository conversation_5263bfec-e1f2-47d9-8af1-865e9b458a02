/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fmantrarajgotecha%2Fkesar_mango%2Fapps%2Fweb%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmantrarajgotecha%2Fkesar_mango%2Fapps%2Fweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fmantrarajgotecha%2Fkesar_mango%2Fapps%2Fweb%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmantrarajgotecha%2Fkesar_mango%2Fapps%2Fweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?b784\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/../../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/../../node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/../../node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"/Users/<USER>/kesar_mango/apps/web/src/app/page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/Users/<USER>/kesar_mango/apps/web/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/../../node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/kesar_mango/apps/web/src/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fmantrarajgotecha%2Fkesar_mango%2Fapps%2Fweb%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmantrarajgotecha%2Fkesar_mango%2Fapps%2Fweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fmantrarajgotecha%2Fkesar_mango%2Fapps%2Fweb%2Fsrc%2Fapp%2Fpage.tsx&server=true!":
/*!************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fmantrarajgotecha%2Fkesar_mango%2Fapps%2Fweb%2Fsrc%2Fapp%2Fpage.tsx&server=true! ***!
  \************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSUyRlVzZXJzJTJGbWFudHJhcmFqZ290ZWNoYSUyRmtlc2FyX21hbmdvJTJGYXBwcyUyRndlYiUyRnNyYyUyRmFwcCUyRnBhZ2UudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL0BrZXNhci1tYW5nby93ZWIvP2I0ZTUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvbWFudHJhcmFqZ290ZWNoYS9rZXNhcl9tYW5nby9hcHBzL3dlYi9zcmMvYXBwL3BhZ2UudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fmantrarajgotecha%2Fkesar_mango%2Fapps%2Fweb%2Fsrc%2Fapp%2Fpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fmantrarajgotecha%2Fkesar_mango%2Fapps%2Fweb%2Fsrc%2Fapp%2Fproviders.tsx&modules=%2FUsers%2Fmantrarajgotecha%2Fkesar_mango%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fmantrarajgotecha%2Fkesar_mango%2Fapps%2Fweb%2Fsrc%2Fapp%2Fglobals.css&modules=%2FUsers%2Fmantrarajgotecha%2Fkesar_mango%2Fnode_modules%2Freact-hot-toast%2Fdist%2Findex.mjs&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fmantrarajgotecha%2Fkesar_mango%2Fapps%2Fweb%2Fsrc%2Fapp%2Fproviders.tsx&modules=%2FUsers%2Fmantrarajgotecha%2Fkesar_mango%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fmantrarajgotecha%2Fkesar_mango%2Fapps%2Fweb%2Fsrc%2Fapp%2Fglobals.css&modules=%2FUsers%2Fmantrarajgotecha%2Fkesar_mango%2Fnode_modules%2Freact-hot-toast%2Fdist%2Findex.mjs&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/providers.tsx */ \"(ssr)/./src/app/providers.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../node_modules/react-hot-toast/dist/index.mjs */ \"(ssr)/../../node_modules/react-hot-toast/dist/index.mjs\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSUyRlVzZXJzJTJGbWFudHJhcmFqZ290ZWNoYSUyRmtlc2FyX21hbmdvJTJGYXBwcyUyRndlYiUyRnNyYyUyRmFwcCUyRnByb3ZpZGVycy50c3gmbW9kdWxlcz0lMkZVc2VycyUyRm1hbnRyYXJhamdvdGVjaGElMkZrZXNhcl9tYW5nbyUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZmb250JTJGZ29vZ2xlJTJGdGFyZ2V0LmNzcyUzRiU3QiUyMnBhdGglMjIlM0ElMjJzcmMlMkZhcHAlMkZsYXlvdXQudHN4JTIyJTJDJTIyaW1wb3J0JTIyJTNBJTIySW50ZXIlMjIlMkMlMjJhcmd1bWVudHMlMjIlM0ElNUIlN0IlMjJzdWJzZXRzJTIyJTNBJTVCJTIybGF0aW4lMjIlNUQlN0QlNUQlMkMlMjJ2YXJpYWJsZU5hbWUlMjIlM0ElMjJpbnRlciUyMiU3RCZtb2R1bGVzPSUyRlVzZXJzJTJGbWFudHJhcmFqZ290ZWNoYSUyRmtlc2FyX21hbmdvJTJGYXBwcyUyRndlYiUyRnNyYyUyRmFwcCUyRmdsb2JhbHMuY3NzJm1vZHVsZXM9JTJGVXNlcnMlMkZtYW50cmFyYWpnb3RlY2hhJTJGa2VzYXJfbWFuZ28lMkZub2RlX21vZHVsZXMlMkZyZWFjdC1ob3QtdG9hc3QlMkZkaXN0JTJGaW5kZXgubWpzJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSwwSkFBdUc7QUFDdkciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9Aa2VzYXItbWFuZ28vd2ViLz8yMDI1Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL21hbnRyYXJhamdvdGVjaGEva2VzYXJfbWFuZ28vYXBwcy93ZWIvc3JjL2FwcC9wcm92aWRlcnMudHN4XCIpO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvbWFudHJhcmFqZ290ZWNoYS9rZXNhcl9tYW5nby9ub2RlX21vZHVsZXMvcmVhY3QtaG90LXRvYXN0L2Rpc3QvaW5kZXgubWpzXCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fmantrarajgotecha%2Fkesar_mango%2Fapps%2Fweb%2Fsrc%2Fapp%2Fproviders.tsx&modules=%2FUsers%2Fmantrarajgotecha%2Fkesar_mango%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fmantrarajgotecha%2Fkesar_mango%2Fapps%2Fweb%2Fsrc%2Fapp%2Fglobals.css&modules=%2FUsers%2Fmantrarajgotecha%2Fkesar_mango%2Fnode_modules%2Freact-hot-toast%2Fdist%2Findex.mjs&server=true!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fmantrarajgotecha%2Fkesar_mango%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fmantrarajgotecha%2Fkesar_mango%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fmantrarajgotecha%2Fkesar_mango%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fmantrarajgotecha%2Fkesar_mango%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fmantrarajgotecha%2Fkesar_mango%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fmantrarajgotecha%2Fkesar_mango%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fmantrarajgotecha%2Fkesar_mango%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fmantrarajgotecha%2Fkesar_mango%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fmantrarajgotecha%2Fkesar_mango%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fmantrarajgotecha%2Fkesar_mango%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fmantrarajgotecha%2Fkesar_mango%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fmantrarajgotecha%2Fkesar_mango%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/app-router.js */ \"(ssr)/../../node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/../../node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/../../node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/../../node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/../../node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/../../node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fmantrarajgotecha%2Fkesar_mango%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fmantrarajgotecha%2Fkesar_mango%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fmantrarajgotecha%2Fkesar_mango%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fmantrarajgotecha%2Fkesar_mango%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fmantrarajgotecha%2Fkesar_mango%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fmantrarajgotecha%2Fkesar_mango%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(ssr)/../../node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _components_layout_Header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/Header */ \"(ssr)/./src/components/layout/Header.tsx\");\n/* harmony import */ var _components_layout_Sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/layout/Sidebar */ \"(ssr)/./src/components/layout/Sidebar.tsx\");\n/* harmony import */ var _components_sports_SportsGrid__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/sports/SportsGrid */ \"(ssr)/./src/components/sports/SportsGrid.tsx\");\n/* harmony import */ var _components_sports_LiveEvents__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/sports/LiveEvents */ \"(ssr)/./src/components/sports/LiveEvents.tsx\");\n/* harmony import */ var _components_betting_BettingSlip__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/betting/BettingSlip */ \"(ssr)/./src/components/betting/BettingSlip.tsx\");\n/* harmony import */ var _components_casino_CasinoLobby__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/casino/CasinoLobby */ \"(ssr)/./src/components/casino/CasinoLobby.tsx\");\n/* harmony import */ var _components_dashboard_StatsOverview__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/dashboard/StatsOverview */ \"(ssr)/./src/components/dashboard/StatsOverview.tsx\");\n/* harmony import */ var _components_sports_TrendingBets__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/sports/TrendingBets */ \"(ssr)/./src/components/sports/TrendingBets.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\nfunction HomePage() {\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"sports\");\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-dark-950 via-dark-900 to-dark-950\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Header__WEBPACK_IMPORTED_MODULE_2__.Header, {\n                onMenuClick: ()=>setSidebarOpen(!sidebarOpen)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/app/page.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Sidebar__WEBPACK_IMPORTED_MODULE_3__.Sidebar, {\n                        isOpen: sidebarOpen,\n                        onClose: ()=>setSidebarOpen(false)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/app/page.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 lg:ml-64\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 lg:p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6\n                                    },\n                                    className: \"mb-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative overflow-hidden rounded-2xl bg-gradient-to-r from-primary-600 via-primary-700 to-secondary-600 p-8 text-white\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-black/20\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/app/page.tsx\",\n                                                lineNumber: 38,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative z-10\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-4xl lg:text-6xl font-bold mb-4\",\n                                                        children: [\n                                                            \"Welcome to \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-yellow-400\",\n                                                                children: \"Kesar Mango\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/app/page.tsx\",\n                                                                lineNumber: 41,\n                                                                columnNumber: 32\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/app/page.tsx\",\n                                                        lineNumber: 40,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl lg:text-2xl mb-6 opacity-90\",\n                                                        children: \"Experience the ultimate betting platform with live odds and instant payouts\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/app/page.tsx\",\n                                                        lineNumber: 43,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.button, {\n                                                                whileHover: {\n                                                                    scale: 1.05\n                                                                },\n                                                                whileTap: {\n                                                                    scale: 0.95\n                                                                },\n                                                                className: \"bg-white text-primary-700 px-8 py-3 rounded-xl font-semibold hover:bg-gray-100 transition-colors\",\n                                                                children: \"Start Betting Now\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/app/page.tsx\",\n                                                                lineNumber: 47,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.button, {\n                                                                whileHover: {\n                                                                    scale: 1.05\n                                                                },\n                                                                whileTap: {\n                                                                    scale: 0.95\n                                                                },\n                                                                className: \"border-2 border-white text-white px-8 py-3 rounded-xl font-semibold hover:bg-white hover:text-primary-700 transition-colors\",\n                                                                children: \"Explore Casino\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/app/page.tsx\",\n                                                                lineNumber: 54,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/app/page.tsx\",\n                                                        lineNumber: 46,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/app/page.tsx\",\n                                                lineNumber: 39,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-4 right-4 w-20 h-20 bg-white/10 rounded-full animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/app/page.tsx\",\n                                                lineNumber: 65,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bottom-4 right-20 w-12 h-12 bg-yellow-400/20 rounded-full animate-bounce\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/app/page.tsx\",\n                                                lineNumber: 66,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/app/page.tsx\",\n                                        lineNumber: 37,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/app/page.tsx\",\n                                    lineNumber: 31,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_StatsOverview__WEBPACK_IMPORTED_MODULE_8__.StatsOverview, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/app/page.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.2\n                                    },\n                                    className: \"mb-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-1 bg-dark-800 p-1 rounded-xl\",\n                                        children: [\n                                            {\n                                                id: \"sports\",\n                                                label: \"Sports Betting\",\n                                                icon: \"⚽\"\n                                            },\n                                            {\n                                                id: \"live\",\n                                                label: \"Live Events\",\n                                                icon: \"\\uD83D\\uDD34\"\n                                            },\n                                            {\n                                                id: \"casino\",\n                                                label: \"Casino Games\",\n                                                icon: \"\\uD83C\\uDFB0\"\n                                            }\n                                        ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setActiveTab(tab.id),\n                                                className: `flex-1 flex items-center justify-center gap-2 py-3 px-4 rounded-lg font-medium transition-all duration-200 ${activeTab === tab.id ? \"bg-primary-600 text-white shadow-lg\" : \"text-gray-400 hover:text-white hover:bg-dark-700\"}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-lg\",\n                                                        children: tab.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/app/page.tsx\",\n                                                        lineNumber: 95,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"hidden sm:inline\",\n                                                        children: tab.label\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/app/page.tsx\",\n                                                        lineNumber: 96,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, tab.id, true, {\n                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/app/page.tsx\",\n                                                lineNumber: 86,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/app/page.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/app/page.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        x: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    transition: {\n                                        duration: 0.4\n                                    },\n                                    children: [\n                                        activeTab === \"sports\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 xl:grid-cols-4 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"xl:col-span-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sports_SportsGrid__WEBPACK_IMPORTED_MODULE_4__.SportsGrid, {}, void 0, false, {\n                                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/app/page.tsx\",\n                                                        lineNumber: 112,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/app/page.tsx\",\n                                                    lineNumber: 111,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sports_TrendingBets__WEBPACK_IMPORTED_MODULE_9__.TrendingBets, {}, void 0, false, {\n                                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/app/page.tsx\",\n                                                        lineNumber: 115,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/app/page.tsx\",\n                                                    lineNumber: 114,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/app/page.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 17\n                                        }, this),\n                                        activeTab === \"live\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sports_LiveEvents__WEBPACK_IMPORTED_MODULE_5__.LiveEvents, {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/app/page.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 40\n                                        }, this),\n                                        activeTab === \"casino\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_casino_CasinoLobby__WEBPACK_IMPORTED_MODULE_7__.CasinoLobby, {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/app/page.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 42\n                                        }, this)\n                                    ]\n                                }, activeTab, true, {\n                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/app/page.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/app/page.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/app/page.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/app/page.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_betting_BettingSlip__WEBPACK_IMPORTED_MODULE_6__.BettingSlip, {}, void 0, false, {\n                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/app/page.tsx\",\n                lineNumber: 129,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/app/page.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/providers.tsx":
/*!*******************************!*\
  !*** ./src/app/providers.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _store_betting_store__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/store/betting-store */ \"(ssr)/./src/store/betting-store.tsx\");\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\nfunction Providers({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_store_betting_store__WEBPACK_IMPORTED_MODULE_1__.BettingProvider, {\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/app/providers.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL3Byb3ZpZGVycy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFHdUQ7QUFNaEQsU0FBU0MsVUFBVSxFQUFFQyxRQUFRLEVBQWtCO0lBQ3BELHFCQUNFLDhEQUFDRixpRUFBZUE7a0JBQ2JFOzs7Ozs7QUFHUCIsInNvdXJjZXMiOlsid2VicGFjazovL0BrZXNhci1tYW5nby93ZWIvLi9zcmMvYXBwL3Byb3ZpZGVycy50c3g/OTMyNiJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgUmVhY3ROb2RlIH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgeyBCZXR0aW5nUHJvdmlkZXIgfSBmcm9tICdAL3N0b3JlL2JldHRpbmctc3RvcmUnXG5cbmludGVyZmFjZSBQcm92aWRlcnNQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdE5vZGVcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIFByb3ZpZGVycyh7IGNoaWxkcmVuIH06IFByb3ZpZGVyc1Byb3BzKSB7XG4gIHJldHVybiAoXG4gICAgPEJldHRpbmdQcm92aWRlcj5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L0JldHRpbmdQcm92aWRlcj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIkJldHRpbmdQcm92aWRlciIsIlByb3ZpZGVycyIsImNoaWxkcmVuIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/app/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/betting/BettingSlip.tsx":
/*!************************************************!*\
  !*** ./src/components/betting/BettingSlip.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BettingSlip: () => (/* binding */ BettingSlip)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/../../node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(ssr)/../../node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_CurrencyDollarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CurrencyDollarIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CurrencyDollarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CurrencyDollarIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CurrencyDollarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CurrencyDollarIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\");\n/* harmony import */ var _store_betting_store__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/betting-store */ \"(ssr)/./src/store/betting-store.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/../../node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ BettingSlip auto */ \n\n\n\n\n\nfunction BettingSlip() {\n    const { isOpen, selections, stake, betType, totalOdds, potentialWin, closeSlip, removeSelection, clearSelections, setStake, setBetType, canPlaceBet } = (0,_store_betting_store__WEBPACK_IMPORTED_MODULE_2__.useBetting)();\n    const [isPlacing, setIsPlacing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handlePlaceBet = async ()=>{\n        if (!canPlaceBet) return;\n        setIsPlacing(true);\n        // Simulate API call\n        try {\n            await new Promise((resolve)=>setTimeout(resolve, 1500));\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success(`Bet placed successfully! Potential win: $${potentialWin.toFixed(2)}`);\n            clearSelections();\n            closeSlip();\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(\"Failed to place bet. Please try again.\");\n        } finally{\n            setIsPlacing(false);\n        }\n    };\n    const handleStakeChange = (value)=>{\n        const numValue = parseFloat(value) || 0;\n        setStake(numValue);\n    };\n    const quickStakeAmounts = [\n        10,\n        25,\n        50,\n        100\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.AnimatePresence, {\n        children: isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    onClick: closeSlip,\n                    className: \"fixed inset-0 bg-black/50 z-40 lg:hidden\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/betting/BettingSlip.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                    initial: {\n                        x: \"100%\"\n                    },\n                    animate: {\n                        x: 0\n                    },\n                    exit: {\n                        x: \"100%\"\n                    },\n                    transition: {\n                        type: \"spring\",\n                        damping: 25,\n                        stiffness: 200\n                    },\n                    className: \"fixed right-0 top-0 h-full w-full max-w-md bg-dark-900 border-l border-dark-700 z-50 flex flex-col\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-4 border-b border-dark-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-lg font-semibold text-white\",\n                                    children: \"Betting Slip\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/betting/BettingSlip.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        selections.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: clearSelections,\n                                            className: \"p-2 rounded-lg hover:bg-dark-700 transition-colors\",\n                                            title: \"Clear all selections\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CurrencyDollarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"h-5 w-5 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/betting/BettingSlip.tsx\",\n                                                lineNumber: 88,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/betting/BettingSlip.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: closeSlip,\n                                            className: \"p-2 rounded-lg hover:bg-dark-700 transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CurrencyDollarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-5 w-5 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/betting/BettingSlip.tsx\",\n                                                lineNumber: 95,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/betting/BettingSlip.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/betting/BettingSlip.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/betting/BettingSlip.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 overflow-y-auto\",\n                            children: selections.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center justify-center h-full p-8 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-16 h-16 bg-dark-700 rounded-full flex items-center justify-center mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CurrencyDollarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-8 w-8 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/betting/BettingSlip.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/betting/BettingSlip.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-white font-medium mb-2\",\n                                        children: \"Your bet slip is empty\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/betting/BettingSlip.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Click on odds to add selections to your bet slip\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/betting/BettingSlip.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/betting/BettingSlip.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 space-y-4\",\n                                children: [\n                                    selections.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm font-medium text-gray-300\",\n                                                children: \"Bet Type\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/betting/BettingSlip.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-1 bg-dark-800 p-1 rounded-lg\",\n                                                children: [\n                                                    {\n                                                        id: \"single\",\n                                                        label: \"Single\"\n                                                    },\n                                                    {\n                                                        id: \"combo\",\n                                                        label: \"Combo\"\n                                                    },\n                                                    {\n                                                        id: \"system\",\n                                                        label: \"System\"\n                                                    }\n                                                ].map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setBetType(type.id),\n                                                        className: `flex-1 py-2 px-3 rounded-md text-sm font-medium transition-colors ${betType === type.id ? \"bg-primary-600 text-white\" : \"text-gray-400 hover:text-white\"}`,\n                                                        children: type.label\n                                                    }, type.id, false, {\n                                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/betting/BettingSlip.tsx\",\n                                                        lineNumber: 124,\n                                                        columnNumber: 27\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/betting/BettingSlip.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/betting/BettingSlip.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: selections.map((selection, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                                initial: {\n                                                    opacity: 0,\n                                                    y: 20\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    y: 0\n                                                },\n                                                exit: {\n                                                    opacity: 0,\n                                                    y: -20\n                                                },\n                                                className: \"bg-dark-800 rounded-lg p-3 border border-dark-600\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start justify-between mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"text-white font-medium text-sm\",\n                                                                        children: selection.eventName\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/betting/BettingSlip.tsx\",\n                                                                        lineNumber: 152,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-400 text-xs\",\n                                                                        children: selection.marketName\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/betting/BettingSlip.tsx\",\n                                                                        lineNumber: 155,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/betting/BettingSlip.tsx\",\n                                                                lineNumber: 151,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>removeSelection(selection.outcomeId),\n                                                                className: \"p-1 rounded hover:bg-dark-700 transition-colors\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CurrencyDollarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-gray-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/betting/BettingSlip.tsx\",\n                                                                    lineNumber: 163,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/betting/BettingSlip.tsx\",\n                                                                lineNumber: 159,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/betting/BettingSlip.tsx\",\n                                                        lineNumber: 150,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white font-medium\",\n                                                                children: selection.outcomeName\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/betting/BettingSlip.tsx\",\n                                                                lineNumber: 168,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: [\n                                                                    selection.isLive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-red-400 text-xs\",\n                                                                        children: \"LIVE\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/betting/BettingSlip.tsx\",\n                                                                        lineNumber: 173,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-primary-400 font-bold\",\n                                                                        children: selection.odds.toFixed(2)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/betting/BettingSlip.tsx\",\n                                                                        lineNumber: 175,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/betting/BettingSlip.tsx\",\n                                                                lineNumber: 171,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/betting/BettingSlip.tsx\",\n                                                        lineNumber: 167,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, selection.outcomeId, true, {\n                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/betting/BettingSlip.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 23\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/betting/BettingSlip.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm font-medium text-gray-300\",\n                                                children: \"Stake Amount\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/betting/BettingSlip.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-4 gap-2\",\n                                                children: quickStakeAmounts.map((amount)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setStake(amount),\n                                                        className: `py-2 px-3 rounded-lg text-sm font-medium transition-colors ${stake === amount ? \"bg-primary-600 text-white\" : \"bg-dark-700 text-gray-300 hover:bg-dark-600\"}`,\n                                                        children: [\n                                                            \"$\",\n                                                            amount\n                                                        ]\n                                                    }, amount, true, {\n                                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/betting/BettingSlip.tsx\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 25\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/betting/BettingSlip.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\",\n                                                        children: \"$\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/betting/BettingSlip.tsx\",\n                                                        lineNumber: 207,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        value: stake || \"\",\n                                                        onChange: (e)=>handleStakeChange(e.target.value),\n                                                        placeholder: \"Enter stake amount\",\n                                                        className: \"w-full pl-8 pr-4 py-3 bg-dark-700 border border-dark-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/betting/BettingSlip.tsx\",\n                                                        lineNumber: 210,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/betting/BettingSlip.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/betting/BettingSlip.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-dark-700 rounded-lg p-4 space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-400\",\n                                                        children: \"Total Odds:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/betting/BettingSlip.tsx\",\n                                                        lineNumber: 223,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white font-medium\",\n                                                        children: totalOdds.toFixed(2)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/betting/BettingSlip.tsx\",\n                                                        lineNumber: 224,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/betting/BettingSlip.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-400\",\n                                                        children: \"Stake:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/betting/BettingSlip.tsx\",\n                                                        lineNumber: 229,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white font-medium\",\n                                                        children: [\n                                                            \"$\",\n                                                            stake.toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/betting/BettingSlip.tsx\",\n                                                        lineNumber: 230,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/betting/BettingSlip.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t border-dark-600 pt-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-300 font-medium\",\n                                                            children: \"Potential Win:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/betting/BettingSlip.tsx\",\n                                                            lineNumber: 236,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-green-400 font-bold text-lg\",\n                                                            children: [\n                                                                \"$\",\n                                                                potentialWin.toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/betting/BettingSlip.tsx\",\n                                                            lineNumber: 237,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/betting/BettingSlip.tsx\",\n                                                    lineNumber: 235,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/betting/BettingSlip.tsx\",\n                                                lineNumber: 234,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/betting/BettingSlip.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/betting/BettingSlip.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/betting/BettingSlip.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 13\n                        }, this),\n                        selections.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 border-t border-dark-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                                whileHover: {\n                                    scale: canPlaceBet ? 1.02 : 1\n                                },\n                                whileTap: {\n                                    scale: canPlaceBet ? 0.98 : 1\n                                },\n                                onClick: handlePlaceBet,\n                                disabled: !canPlaceBet || isPlacing,\n                                className: `w-full py-3 rounded-lg font-semibold transition-all duration-200 ${canPlaceBet && !isPlacing ? \"bg-primary-600 hover:bg-primary-700 text-white\" : \"bg-dark-600 text-gray-400 cursor-not-allowed\"}`,\n                                children: isPlacing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/betting/BettingSlip.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 23\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Placing Bet...\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/betting/BettingSlip.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 23\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/betting/BettingSlip.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 21\n                                }, this) : `Place Bet - $${potentialWin.toFixed(2)}`\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/betting/BettingSlip.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/betting/BettingSlip.tsx\",\n                            lineNumber: 249,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/betting/BettingSlip.tsx\",\n                    lineNumber: 71,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/betting/BettingSlip.tsx\",\n        lineNumber: 58,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/betting/BettingSlip.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/casino/CasinoLobby.tsx":
/*!***********************************************!*\
  !*** ./src/components/casino/CasinoLobby.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CasinoLobby: () => (/* binding */ CasinoLobby)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/../../node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_PlayIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=PlayIcon,SparklesIcon,StarIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_PlayIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=PlayIcon,SparklesIcon,StarIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/PlayIcon.js\");\n/* harmony import */ var _barrel_optimize_names_PlayIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=PlayIcon,SparklesIcon,StarIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/StarIcon.js\");\n/* harmony import */ var _data_mock_data__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/data/mock-data */ \"(ssr)/./src/data/mock-data.ts\");\n/* __next_internal_client_entry_do_not_use__ CasinoLobby auto */ \n\n\n\n\nfunction CasinoLobby() {\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const categories = [\n        {\n            id: \"all\",\n            label: \"All Games\",\n            count: _data_mock_data__WEBPACK_IMPORTED_MODULE_2__.mockCasinoGames.length\n        },\n        {\n            id: \"slots\",\n            label: \"Slots\",\n            count: _data_mock_data__WEBPACK_IMPORTED_MODULE_2__.mockCasinoGames.filter((g)=>g.category === \"Slots\").length\n        },\n        {\n            id: \"live\",\n            label: \"Live Casino\",\n            count: _data_mock_data__WEBPACK_IMPORTED_MODULE_2__.mockCasinoGames.filter((g)=>g.isLive).length\n        },\n        {\n            id: \"featured\",\n            label: \"Featured\",\n            count: _data_mock_data__WEBPACK_IMPORTED_MODULE_2__.mockCasinoGames.filter((g)=>g.isFeatured).length\n        }\n    ];\n    const filteredGames = _data_mock_data__WEBPACK_IMPORTED_MODULE_2__.mockCasinoGames.filter((game)=>{\n        if (selectedCategory === \"all\") return true;\n        if (selectedCategory === \"slots\") return game.category === \"Slots\";\n        if (selectedCategory === \"live\") return game.isLive;\n        if (selectedCategory === \"featured\") return game.isFeatured;\n        return true;\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PlayIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        className: \"h-6 w-6 text-purple-400\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-white\",\n                        children: \"Casino Games\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-wrap gap-2\",\n                children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n                        whileHover: {\n                            scale: 1.05\n                        },\n                        whileTap: {\n                            scale: 0.95\n                        },\n                        onClick: ()=>setSelectedCategory(category.id),\n                        className: `flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-200 ${selectedCategory === category.id ? \"bg-purple-600 text-white\" : \"bg-dark-700 text-gray-300 hover:bg-dark-600 hover:text-white\"}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: category.label\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs bg-black/20 px-2 py-1 rounded-full\",\n                                children: category.count\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, category.id, true, {\n                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4\",\n                children: filteredGames.map((game, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.4,\n                            delay: index * 0.05\n                        },\n                        whileHover: {\n                            scale: 1.05\n                        },\n                        className: \"group cursor-pointer\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative bg-dark-800 rounded-xl overflow-hidden border border-dark-700 hover:border-purple-500/50 transition-all duration-300\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"aspect-square bg-gradient-to-br from-purple-500/20 to-pink-500/20 flex items-center justify-center relative overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-full bg-gradient-to-br from-purple-600 to-pink-600 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PlayIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                className: \"h-12 w-12 text-white/80\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                                                lineNumber: 77,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-2 left-2 flex flex-col space-y-1\",\n                                            children: [\n                                                game.isNew && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"bg-green-500 text-white text-xs px-2 py-1 rounded-full font-medium\",\n                                                    children: \"NEW\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                                                    lineNumber: 83,\n                                                    columnNumber: 21\n                                                }, this),\n                                                game.isFeatured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"bg-yellow-500 text-black text-xs px-2 py-1 rounded-full font-medium\",\n                                                    children: \"⭐ FEATURED\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                                                    lineNumber: 88,\n                                                    columnNumber: 21\n                                                }, this),\n                                                game.isLive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"bg-red-500 text-white text-xs px-2 py-1 rounded-full font-medium flex items-center space-x-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-1 h-1 bg-white rounded-full animate-pulse\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                                                            lineNumber: 94,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"LIVE\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                                                            lineNumber: 95,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                                                    lineNumber: 93,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                initial: {\n                                                    scale: 0\n                                                },\n                                                whileHover: {\n                                                    scale: 1\n                                                },\n                                                className: \"w-16 h-16 bg-white rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PlayIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"h-8 w-8 text-purple-600 ml-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                                                    lineNumber: 107,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-2 right-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-black/70 text-white text-xs px-2 py-1 rounded-full\",\n                                                children: [\n                                                    \"RTP \",\n                                                    game.rtp,\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                                                lineNumber: 113,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-white font-medium text-sm mb-1 group-hover:text-purple-400 transition-colors\",\n                                            children: game.name\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400 text-xs mb-2\",\n                                            children: game.provider\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between text-xs\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-500\",\n                                                    children: [\n                                                        \"$\",\n                                                        game.minBet,\n                                                        \" - $\",\n                                                        game.maxBet\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                                                    lineNumber: 127,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PlayIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            className: \"h-3 w-3 text-yellow-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                                                            lineNumber: 131,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-400\",\n                                                            children: game.popularity\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                                                            lineNumber: 132,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                                                    lineNumber: 130,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 13\n                        }, this)\n                    }, game.id, false, {\n                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                lineNumber: 62,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n                    whileHover: {\n                        scale: 1.05\n                    },\n                    whileTap: {\n                        scale: 0.95\n                    },\n                    className: \"bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-8 py-3 rounded-xl font-semibold transition-all duration-200\",\n                    children: \"Load More Games\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                lineNumber: 142,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.6,\n                    delay: 0.3\n                },\n                className: \"bg-gradient-to-r from-yellow-500 via-orange-500 to-red-500 rounded-xl p-6 text-white relative overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-black/20\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative z-10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-bold mb-2\",\n                                            children: \"\\uD83C\\uDFB0 Progressive Jackpot\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-lg opacity-90\",\n                                            children: \"Current jackpot amount\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-right\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-4xl font-bold\",\n                                            children: \"$2,847,593\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm opacity-75\",\n                                            children: \"and growing...\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-4 right-4 w-16 h-16 bg-white/10 rounded-full animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-4 left-20 w-8 h-8 bg-yellow-400/20 rounded-full animate-bounce\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n                lineNumber: 153,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/casino/CasinoLobby.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/dashboard/StatsOverview.tsx":
/*!****************************************************!*\
  !*** ./src/components/dashboard/StatsOverview.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StatsOverview: () => (/* binding */ StatsOverview)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(ssr)/../../node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_CurrencyDollarIcon_FireIcon_TrophyIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=CurrencyDollarIcon,FireIcon,TrophyIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/UsersIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CurrencyDollarIcon_FireIcon_TrophyIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CurrencyDollarIcon,FireIcon,TrophyIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CurrencyDollarIcon_FireIcon_TrophyIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CurrencyDollarIcon,FireIcon,TrophyIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/TrophyIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CurrencyDollarIcon_FireIcon_TrophyIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CurrencyDollarIcon,FireIcon,TrophyIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/FireIcon.js\");\n/* harmony import */ var _data_mock_data__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/data/mock-data */ \"(ssr)/./src/data/mock-data.ts\");\n/* __next_internal_client_entry_do_not_use__ StatsOverview auto */ \n\n\n\nconst stats = [\n    {\n        id: \"users\",\n        label: \"Active Users\",\n        value: _data_mock_data__WEBPACK_IMPORTED_MODULE_1__.mockPlatformStats.totalUsers.toLocaleString(),\n        change: \"+12.5%\",\n        changeType: \"positive\",\n        icon: _barrel_optimize_names_CurrencyDollarIcon_FireIcon_TrophyIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        color: \"from-blue-500 to-blue-600\"\n    },\n    {\n        id: \"volume\",\n        label: \"Total Volume\",\n        value: `$${(_data_mock_data__WEBPACK_IMPORTED_MODULE_1__.mockPlatformStats.totalVolume / 1000000).toFixed(1)}M`,\n        change: \"+8.2%\",\n        changeType: \"positive\",\n        icon: _barrel_optimize_names_CurrencyDollarIcon_FireIcon_TrophyIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        color: \"from-green-500 to-green-600\"\n    },\n    {\n        id: \"bets\",\n        label: \"Total Bets\",\n        value: (_data_mock_data__WEBPACK_IMPORTED_MODULE_1__.mockPlatformStats.totalBets / 1000000).toFixed(1) + \"M\",\n        change: \"+15.3%\",\n        changeType: \"positive\",\n        icon: _barrel_optimize_names_CurrencyDollarIcon_FireIcon_TrophyIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        color: \"from-purple-500 to-purple-600\"\n    },\n    {\n        id: \"live\",\n        label: \"Live Events\",\n        value: _data_mock_data__WEBPACK_IMPORTED_MODULE_1__.mockPlatformStats.liveEvents.toString(),\n        change: \"+5\",\n        changeType: \"positive\",\n        icon: _barrel_optimize_names_CurrencyDollarIcon_FireIcon_TrophyIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        color: \"from-red-500 to-red-600\"\n    }\n];\nfunction StatsOverview() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6 mb-8\",\n        children: [\n            stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.6,\n                        delay: index * 0.1\n                    },\n                    className: \"bg-dark-800 rounded-xl p-6 border border-dark-700 hover:border-dark-600 transition-colors\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `p-3 rounded-lg bg-gradient-to-r ${stat.color}`,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                        className: \"h-6 w-6 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx\",\n                                        lineNumber: 64,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `text-sm font-medium ${stat.changeType === \"positive\" ? \"text-green-400\" : \"text-red-400\"}`,\n                                    children: stat.change\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-2xl font-bold text-white mb-1\",\n                                    children: stat.value\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 text-sm\",\n                                    children: stat.label\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, stat.id, true, {\n                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 9\n                }, this)),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.6,\n                    delay: 0.4\n                },\n                className: \"md:col-span-2 xl:col-span-4 bg-dark-800 rounded-xl p-6 border border-dark-700\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-white flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CurrencyDollarIcon_FireIcon_TrophyIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-5 w-5 text-yellow-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Recent Big Winners\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-gray-400\",\n                                children: \"Live updates\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                        children: _data_mock_data__WEBPACK_IMPORTED_MODULE_1__.mockRecentWinners.map((winner, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    x: -20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                transition: {\n                                    duration: 0.4,\n                                    delay: 0.5 + index * 0.1\n                                },\n                                className: \"bg-dark-700 rounded-lg p-4 border border-dark-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-medium\",\n                                                children: winner.username\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-green-400 font-bold\",\n                                                children: [\n                                                    \"$\",\n                                                    winner.amount.toLocaleString()\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-400\",\n                                                children: winner.game\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-500\",\n                                                children: winner.time\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx\",\n        lineNumber: 53,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/dashboard/StatsOverview.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Header: () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/../../node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_BellIcon_MagnifyingGlassIcon_UserCircleIcon_WalletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,BellIcon,MagnifyingGlassIcon,UserCircleIcon,WalletIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_BellIcon_MagnifyingGlassIcon_UserCircleIcon_WalletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,BellIcon,MagnifyingGlassIcon,UserCircleIcon,WalletIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_BellIcon_MagnifyingGlassIcon_UserCircleIcon_WalletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,BellIcon,MagnifyingGlassIcon,UserCircleIcon,WalletIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/WalletIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_BellIcon_MagnifyingGlassIcon_UserCircleIcon_WalletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,BellIcon,MagnifyingGlassIcon,UserCircleIcon,WalletIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/BellIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_BellIcon_MagnifyingGlassIcon_UserCircleIcon_WalletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,BellIcon,MagnifyingGlassIcon,UserCircleIcon,WalletIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/UserCircleIcon.js\");\n/* harmony import */ var _store_betting_store__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/betting-store */ \"(ssr)/./src/store/betting-store.tsx\");\n/* __next_internal_client_entry_do_not_use__ Header auto */ \n\n\n\n\nfunction Header({ onMenuClick }) {\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const { selectionCount, toggleSlip } = (0,_store_betting_store__WEBPACK_IMPORTED_MODULE_2__.useBetting)();\n    // Mock user data\n    const user = {\n        username: \"BetMaster\",\n        balance: 1250.50,\n        currency: \"USD\",\n        avatar: null\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"sticky top-0 z-50 bg-dark-900/95 backdrop-blur-md border-b border-dark-700\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 lg:px-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between h-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onMenuClick,\n                                    className: \"lg:hidden p-2 rounded-lg hover:bg-dark-700 transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BellIcon_MagnifyingGlassIcon_UserCircleIcon_WalletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"h-6 w-6 text-gray-300\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                        lineNumber: 41,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                    lineNumber: 37,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        x: -20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-bold text-lg\",\n                                                children: \"K\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                                lineNumber: 51,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                            lineNumber: 50,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"hidden sm:block text-xl font-bold text-white\",\n                                            children: \"Kesar Mango\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                            lineNumber: 53,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                    lineNumber: 45,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                            lineNumber: 35,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex flex-1 max-w-md mx-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative w-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BellIcon_MagnifyingGlassIcon_UserCircleIcon_WalletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                        lineNumber: 62,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"Search events, teams, or games...\",\n                                        value: searchQuery,\n                                        onChange: (e)=>setSearchQuery(e.target.value),\n                                        className: \"w-full pl-10 pr-4 py-2 bg-dark-800 border border-dark-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    className: \"hidden sm:flex items-center space-x-2 bg-dark-800 px-4 py-2 rounded-lg border border-dark-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BellIcon_MagnifyingGlassIcon_UserCircleIcon_WalletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-5 w-5 text-primary-400\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                            lineNumber: 80,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-400\",\n                                                    children: \"Balance\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                                    lineNumber: 82,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-semibold text-white\",\n                                                    children: [\n                                                        \"$\",\n                                                        user.balance.toFixed(2)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                                    lineNumber: 83,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    onClick: toggleSlip,\n                                    className: \"relative bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg font-medium transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"hidden sm:inline\",\n                                            children: \"Bet Slip\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"sm:hidden\",\n                                            children: \"Slip\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 15\n                                        }, this),\n                                        selectionCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.span, {\n                                            initial: {\n                                                scale: 0\n                                            },\n                                            animate: {\n                                                scale: 1\n                                            },\n                                            className: \"absolute -top-2 -right-2 bg-secondary-500 text-white text-xs rounded-full h-6 w-6 flex items-center justify-center font-bold\",\n                                            children: selectionCount\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    className: \"relative p-2 rounded-lg hover:bg-dark-700 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BellIcon_MagnifyingGlassIcon_UserCircleIcon_WalletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-6 w-6 text-gray-300\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n                                        whileHover: {\n                                            scale: 1.05\n                                        },\n                                        whileTap: {\n                                            scale: 0.95\n                                        },\n                                        className: \"flex items-center space-x-2 p-2 rounded-lg hover:bg-dark-700 transition-colors\",\n                                        children: [\n                                            user.avatar ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: user.avatar,\n                                                alt: user.username,\n                                                className: \"h-8 w-8 rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BellIcon_MagnifyingGlassIcon_UserCircleIcon_WalletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-8 w-8 text-gray-300\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden lg:block text-white font-medium\",\n                                                children: user.username\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"md:hidden px-4 pb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BellIcon_MagnifyingGlassIcon_UserCircleIcon_WalletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            placeholder: \"Search events, teams, or games...\",\n                            value: searchQuery,\n                            onChange: (e)=>setSearchQuery(e.target.value),\n                            className: \"w-full pl-10 pr-4 py-2 bg-dark-800 border border-dark-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                    lineNumber: 146,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                lineNumber: 145,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Sidebar.tsx":
/*!*******************************************!*\
  !*** ./src/components/layout/Sidebar.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(ssr)/../../node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(ssr)/../../node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_CogIcon_HomeIcon_PlayIcon_SparklesIcon_TrophyIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,CogIcon,HomeIcon,PlayIcon,SparklesIcon,TrophyIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/HomeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_CogIcon_HomeIcon_PlayIcon_SparklesIcon_TrophyIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,CogIcon,HomeIcon,PlayIcon,SparklesIcon,TrophyIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/TrophyIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_CogIcon_HomeIcon_PlayIcon_SparklesIcon_TrophyIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,CogIcon,HomeIcon,PlayIcon,SparklesIcon,TrophyIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/PlayIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_CogIcon_HomeIcon_PlayIcon_SparklesIcon_TrophyIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,CogIcon,HomeIcon,PlayIcon,SparklesIcon,TrophyIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_CogIcon_HomeIcon_PlayIcon_SparklesIcon_TrophyIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,CogIcon,HomeIcon,PlayIcon,SparklesIcon,TrophyIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_CogIcon_HomeIcon_PlayIcon_SparklesIcon_TrophyIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,CogIcon,HomeIcon,PlayIcon,SparklesIcon,TrophyIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/CogIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_CogIcon_HomeIcon_PlayIcon_SparklesIcon_TrophyIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,CogIcon,HomeIcon,PlayIcon,SparklesIcon,TrophyIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _data_mock_data__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/data/mock-data */ \"(ssr)/./src/data/mock-data.ts\");\n/* __next_internal_client_entry_do_not_use__ Sidebar auto */ \n\n\n\nconst navigationItems = [\n    {\n        id: \"home\",\n        label: \"Home\",\n        icon: _barrel_optimize_names_ChartBarIcon_CogIcon_HomeIcon_PlayIcon_SparklesIcon_TrophyIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        href: \"/\"\n    },\n    {\n        id: \"sports\",\n        label: \"Sports\",\n        icon: _barrel_optimize_names_ChartBarIcon_CogIcon_HomeIcon_PlayIcon_SparklesIcon_TrophyIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        href: \"/sports\"\n    },\n    {\n        id: \"live\",\n        label: \"Live Betting\",\n        icon: _barrel_optimize_names_ChartBarIcon_CogIcon_HomeIcon_PlayIcon_SparklesIcon_TrophyIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        href: \"/live\",\n        badge: \"156\"\n    },\n    {\n        id: \"casino\",\n        label: \"Casino\",\n        icon: _barrel_optimize_names_ChartBarIcon_CogIcon_HomeIcon_PlayIcon_SparklesIcon_TrophyIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        href: \"/casino\"\n    },\n    {\n        id: \"statistics\",\n        label: \"Statistics\",\n        icon: _barrel_optimize_names_ChartBarIcon_CogIcon_HomeIcon_PlayIcon_SparklesIcon_TrophyIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        href: \"/stats\"\n    },\n    {\n        id: \"settings\",\n        label: \"Settings\",\n        icon: _barrel_optimize_names_ChartBarIcon_CogIcon_HomeIcon_PlayIcon_SparklesIcon_TrophyIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        href: \"/settings\"\n    }\n];\nfunction Sidebar({ isOpen, onClose }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.AnimatePresence, {\n                children: isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    onClick: onClose,\n                    className: \"fixed inset-0 bg-black/50 z-40 lg:hidden\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.aside, {\n                initial: {\n                    x: -320\n                },\n                animate: {\n                    x: isOpen ? 0 : -320\n                },\n                transition: {\n                    type: \"spring\",\n                    damping: 25,\n                    stiffness: 200\n                },\n                className: \"fixed left-0 top-0 h-full w-80 bg-dark-900 border-r border-dark-700 z-50 lg:translate-x-0 lg:static lg:z-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col h-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-6 border-b border-dark-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-xl flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-bold text-xl\",\n                                                children: \"K\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                                                lineNumber: 57,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                                            lineNumber: 56,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-white font-bold text-lg\",\n                                                    children: \"Kesar Mango\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                                                    lineNumber: 60,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-400 text-sm\",\n                                                    children: \"Premium Betting\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                                                    lineNumber: 61,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onClose,\n                                    className: \"lg:hidden p-2 rounded-lg hover:bg-dark-700 transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_CogIcon_HomeIcon_PlayIcon_SparklesIcon_TrophyIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-6 w-6 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"flex-1 p-4 space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-gray-400 text-sm font-medium mb-3 uppercase tracking-wider\",\n                                            children: \"Navigation\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                                            lineNumber: 75,\n                                            columnNumber: 15\n                                        }, this),\n                                        navigationItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.a, {\n                                                href: item.href,\n                                                whileHover: {\n                                                    x: 4\n                                                },\n                                                className: \"flex items-center justify-between p-3 rounded-lg hover:bg-dark-700 transition-colors group\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                                className: \"h-5 w-5 text-gray-400 group-hover:text-primary-400 transition-colors\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                                                                lineNumber: 86,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-300 group-hover:text-white transition-colors\",\n                                                                children: item.label\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                                                                lineNumber: 87,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                                                        lineNumber: 85,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    item.badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"bg-primary-600 text-white text-xs px-2 py-1 rounded-full\",\n                                                        children: item.badge\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                                                        lineNumber: 92,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, item.id, true, {\n                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                                                lineNumber: 79,\n                                                columnNumber: 17\n                                            }, this))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-gray-400 text-sm font-medium mb-3 uppercase tracking-wider\",\n                                            children: \"Popular Sports\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-1\",\n                                            children: _data_mock_data__WEBPACK_IMPORTED_MODULE_1__.mockSports.slice(0, 6).map((sport)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.a, {\n                                                    href: `/sports/${sport.slug}`,\n                                                    whileHover: {\n                                                        x: 4\n                                                    },\n                                                    className: \"flex items-center justify-between p-2 rounded-lg hover:bg-dark-700 transition-colors group\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-lg\",\n                                                                    children: sport.icon\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                                                                    lineNumber: 114,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-300 group-hover:text-white transition-colors text-sm\",\n                                                                    children: sport.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                                                                    lineNumber: 115,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                                                            lineNumber: 113,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-500 text-xs\",\n                                                            children: sport.eventCount\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                                                            lineNumber: 119,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, sport.id, true, {\n                                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                                                    lineNumber: 107,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                whileHover: {\n                                    scale: 1.02\n                                },\n                                className: \"bg-gradient-to-r from-primary-600 to-secondary-600 rounded-xl p-4 text-white cursor-pointer\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_CogIcon_HomeIcon_PlayIcon_SparklesIcon_TrophyIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"h-6 w-6\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                                                    lineNumber: 136,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-semibold text-sm\",\n                                                        children: \"Welcome Bonus\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                                                        lineNumber: 139,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs opacity-90\",\n                                                        children: \"Get 100% up to $500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                                                        lineNumber: 140,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.button, {\n                                        whileHover: {\n                                            scale: 1.05\n                                        },\n                                        whileTap: {\n                                            scale: 0.95\n                                        },\n                                        className: \"w-full mt-3 bg-white text-primary-700 py-2 rounded-lg text-sm font-semibold hover:bg-gray-100 transition-colors\",\n                                        children: \"Claim Now\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 border-t border-dark-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center text-gray-500 text-xs\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"\\xa9 2024 Kesar Mango\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Licensed & Regulated\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/sports/LiveEvents.tsx":
/*!**********************************************!*\
  !*** ./src/components/sports/LiveEvents.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LiveEvents: () => (/* binding */ LiveEvents)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(ssr)/../../node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_ClockIcon_FireIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ClockIcon,FireIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/FireIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ClockIcon_FireIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ClockIcon,FireIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _data_mock_data__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/data/mock-data */ \"(ssr)/./src/data/mock-data.ts\");\n/* harmony import */ var _store_betting_store__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/betting-store */ \"(ssr)/./src/store/betting-store.tsx\");\n/* __next_internal_client_entry_do_not_use__ LiveEvents auto */ \n\n\n\n\n\nfunction LiveEvents() {\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { addSelection } = (0,_store_betting_store__WEBPACK_IMPORTED_MODULE_3__.useBetting)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setMounted(true);\n    }, []);\n    const liveEvents = _data_mock_data__WEBPACK_IMPORTED_MODULE_2__.mockEvents.filter((event)=>event.isLive);\n    const handleOddsClick = (event, market, outcome)=>{\n        const selection = {\n            outcomeId: outcome.id,\n            eventId: event.id,\n            marketId: market.id,\n            eventName: `${event.homeTeam.name} vs ${event.awayTeam.name}`,\n            marketName: market.name,\n            outcomeName: outcome.name,\n            odds: outcome.odds,\n            isLive: event.isLive\n        };\n        addSelection(selection);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-3 h-3 bg-red-500 rounded-full animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                                lineNumber: 39,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_FireIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"h-6 w-6 text-red-400\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-white\",\n                        children: \"Live Events\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"bg-red-500 text-white text-sm px-3 py-1 rounded-full\",\n                        children: [\n                            liveEvents.length,\n                            \" Live\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: liveEvents.map((event, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            scale: 0.95\n                        },\n                        animate: {\n                            opacity: 1,\n                            scale: 1\n                        },\n                        transition: {\n                            duration: 0.4,\n                            delay: index * 0.1\n                        },\n                        className: \"bg-gradient-to-br from-dark-800 to-dark-900 rounded-xl border border-red-500/30 overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-red-500/10 border-b border-red-500/30 p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-red-500 rounded-full animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                                                    lineNumber: 62,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-400 font-semibold text-sm\",\n                                                    children: \"LIVE\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                                                    lineNumber: 63,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-white font-bold\",\n                                            children: [\n                                                event.minute,\n                                                \"' \",\n                                                event.period\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                                            lineNumber: 65,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                                lineNumber: 59,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-10 h-10 bg-dark-600 rounded-full flex items-center justify-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-bold text-white\",\n                                                                        children: event.homeTeam.name.charAt(0)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                                                                        lineNumber: 79,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                                                                    lineNumber: 78,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-white font-medium\",\n                                                                    children: event.homeTeam.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                                                                    lineNumber: 83,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                                                            lineNumber: 77,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-3xl font-bold text-white\",\n                                                            children: event.homeScore\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                                                            lineNumber: 85,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                                                    lineNumber: 76,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-10 h-10 bg-dark-600 rounded-full flex items-center justify-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-bold text-white\",\n                                                                        children: event.awayTeam.name.charAt(0)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                                                                        lineNumber: 94,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                                                                    lineNumber: 93,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-white font-medium\",\n                                                                    children: event.awayTeam.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                                                                    lineNumber: 98,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                                                            lineNumber: 92,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-3xl font-bold text-white\",\n                                                            children: event.awayScore\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                                                            lineNumber: 100,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                                                    lineNumber: 91,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                                            lineNumber: 74,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: event.markets.map((market)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-gray-400 text-sm font-medium mb-3\",\n                                                        children: market.name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                                                        lineNumber: 111,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-3 gap-2\",\n                                                        children: market.outcomes.map((outcome)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                                                                whileHover: {\n                                                                    scale: 1.05\n                                                                },\n                                                                whileTap: {\n                                                                    scale: 0.95\n                                                                },\n                                                                onClick: ()=>handleOddsClick(event, market, outcome),\n                                                                className: \"bg-dark-700 hover:bg-dark-600 border border-dark-600 hover:border-primary-500 rounded-lg p-3 transition-all duration-200 group\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-white font-medium text-sm mb-1 group-hover:text-primary-400\",\n                                                                        children: outcome.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                                                                        lineNumber: 123,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-primary-400 font-bold text-lg\",\n                                                                                children: outcome.odds.toFixed(2)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                                                                                lineNumber: 127,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            outcome.trend && outcome.trend !== \"neutral\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.span, {\n                                                                                initial: {\n                                                                                    scale: 0\n                                                                                },\n                                                                                animate: {\n                                                                                    scale: 1\n                                                                                },\n                                                                                className: `text-sm ${outcome.trend === \"up\" ? \"text-green-400\" : \"text-red-400\"}`,\n                                                                                children: outcome.trend === \"up\" ? \"↗\" : \"↘\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                                                                                lineNumber: 131,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                                                                        lineNumber: 126,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, outcome.id, true, {\n                                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                                                                lineNumber: 116,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                                                        lineNumber: 114,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, market.id, true, {\n                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                                                lineNumber: 110,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, event.id, true, {\n                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, this),\n            liveEvents.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_FireIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"h-16 w-16 text-gray-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-semibold text-white mb-2\",\n                        children: \"No Live Events\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400\",\n                        children: \"Check back soon for live betting opportunities\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n                lineNumber: 154,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/sports/LiveEvents.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/sports/SportsGrid.tsx":
/*!**********************************************!*\
  !*** ./src/components/sports/SportsGrid.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SportsGrid: () => (/* binding */ SportsGrid)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/../../node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_ClockIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ClockIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _data_mock_data__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/data/mock-data */ \"(ssr)/./src/data/mock-data.ts\");\n/* harmony import */ var _store_betting_store__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/betting-store */ \"(ssr)/./src/store/betting-store.tsx\");\n/* __next_internal_client_entry_do_not_use__ SportsGrid auto */ \n\n\n\n\n\nfunction SportsGrid() {\n    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { addSelection } = (0,_store_betting_store__WEBPACK_IMPORTED_MODULE_3__.useBetting)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setMounted(true);\n    }, []);\n    const filteredEvents = _data_mock_data__WEBPACK_IMPORTED_MODULE_2__.mockEvents.filter((event)=>{\n        if (filter === \"live\") return event.isLive;\n        if (filter === \"upcoming\") return !event.isLive;\n        return true;\n    });\n    const handleOddsClick = (event, market, outcome)=>{\n        const selection = {\n            outcomeId: outcome.id,\n            eventId: event.id,\n            marketId: market.id,\n            eventName: `${event.homeTeam.name} vs ${event.awayTeam.name}`,\n            marketName: market.name,\n            outcomeName: outcome.name,\n            odds: outcome.odds,\n            isLive: event.isLive\n        };\n        addSelection(selection);\n    };\n    const formatTime = (dateString)=>{\n        if (!mounted) return \"--:--\" // Prevent hydration mismatch\n        ;\n        const date = new Date(dateString);\n        return date.toLocaleTimeString(\"en-US\", {\n            hour: \"2-digit\",\n            minute: \"2-digit\",\n            hour12: false\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex space-x-1 bg-dark-800 p-1 rounded-lg\",\n                children: [\n                    {\n                        id: \"all\",\n                        label: \"All Events\"\n                    },\n                    {\n                        id: \"live\",\n                        label: \"Live\"\n                    },\n                    {\n                        id: \"upcoming\",\n                        label: \"Upcoming\"\n                    }\n                ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setFilter(tab.id),\n                        className: `flex-1 py-2 px-4 rounded-md font-medium transition-all duration-200 ${filter === tab.id ? \"bg-primary-600 text-white\" : \"text-gray-400 hover:text-white hover:bg-dark-700\"}`,\n                        children: tab.label\n                    }, tab.id, false, {\n                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/SportsGrid.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/SportsGrid.tsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: filteredEvents.map((event, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.4,\n                            delay: index * 0.1\n                        },\n                        className: \"bg-dark-800 rounded-xl border border-dark-700 hover:border-dark-600 transition-colors overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 border-b border-dark-700\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                event.isLive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-red-500 rounded-full animate-pulse\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/SportsGrid.tsx\",\n                                                            lineNumber: 88,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-red-400 text-sm font-medium\",\n                                                            children: \"LIVE\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/SportsGrid.tsx\",\n                                                            lineNumber: 89,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/SportsGrid.tsx\",\n                                                    lineNumber: 87,\n                                                    columnNumber: 21\n                                                }, this),\n                                                !event.isLive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2 text-gray-400\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/SportsGrid.tsx\",\n                                                            lineNumber: 94,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: formatTime(event.startTime)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/SportsGrid.tsx\",\n                                                            lineNumber: 95,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/SportsGrid.tsx\",\n                                                    lineNumber: 93,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/SportsGrid.tsx\",\n                                            lineNumber: 85,\n                                            columnNumber: 17\n                                        }, this),\n                                        event.isLive && event.minute && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-primary-400 font-medium\",\n                                            children: [\n                                                event.minute,\n                                                \"' \",\n                                                event.period\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/SportsGrid.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/SportsGrid.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/SportsGrid.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3 mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-8 h-8 bg-dark-600 rounded-full flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs font-bold text-white\",\n                                                                children: event.homeTeam.name.charAt(0)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/SportsGrid.tsx\",\n                                                                lineNumber: 114,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/SportsGrid.tsx\",\n                                                            lineNumber: 113,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white font-medium\",\n                                                            children: event.homeTeam.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/SportsGrid.tsx\",\n                                                            lineNumber: 118,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        event.isLive && event.homeScore !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-2xl font-bold text-white\",\n                                                            children: event.homeScore\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/SportsGrid.tsx\",\n                                                            lineNumber: 120,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/SportsGrid.tsx\",\n                                                    lineNumber: 112,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-8 h-8 bg-dark-600 rounded-full flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs font-bold text-white\",\n                                                                children: event.awayTeam.name.charAt(0)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/SportsGrid.tsx\",\n                                                                lineNumber: 125,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/SportsGrid.tsx\",\n                                                            lineNumber: 124,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white font-medium\",\n                                                            children: event.awayTeam.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/SportsGrid.tsx\",\n                                                            lineNumber: 129,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        event.isLive && event.awayScore !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-2xl font-bold text-white\",\n                                                            children: event.awayScore\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/SportsGrid.tsx\",\n                                                            lineNumber: 131,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/SportsGrid.tsx\",\n                                                    lineNumber: 123,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/SportsGrid.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/SportsGrid.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: event.markets.map((market)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-gray-400 text-sm font-medium mb-2\",\n                                                        children: market.name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/SportsGrid.tsx\",\n                                                        lineNumber: 141,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 md:grid-cols-3 gap-2\",\n                                                        children: market.outcomes.map((outcome)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n                                                                whileHover: {\n                                                                    scale: 1.02\n                                                                },\n                                                                whileTap: {\n                                                                    scale: 0.98\n                                                                },\n                                                                onClick: ()=>handleOddsClick(event, market, outcome),\n                                                                className: `p-3 rounded-lg border transition-all duration-200 ${outcome.trend === \"up\" ? \"bg-green-500/10 border-green-500/30 hover:bg-green-500/20\" : outcome.trend === \"down\" ? \"bg-red-500/10 border-red-500/30 hover:bg-red-500/20\" : \"bg-dark-700 border-dark-600 hover:bg-dark-600\"}`,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-white font-medium text-sm mb-1\",\n                                                                        children: outcome.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/SportsGrid.tsx\",\n                                                                        lineNumber: 159,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-primary-400 font-bold\",\n                                                                                children: outcome.odds.toFixed(2)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/SportsGrid.tsx\",\n                                                                                lineNumber: 163,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            outcome.trend && outcome.trend !== \"neutral\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: `text-xs ${outcome.trend === \"up\" ? \"text-green-400\" : \"text-red-400\"}`,\n                                                                                children: outcome.trend === \"up\" ? \"↗\" : \"↘\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/SportsGrid.tsx\",\n                                                                                lineNumber: 167,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/SportsGrid.tsx\",\n                                                                        lineNumber: 162,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, outcome.id, true, {\n                                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/SportsGrid.tsx\",\n                                                                lineNumber: 146,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/SportsGrid.tsx\",\n                                                        lineNumber: 144,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, market.id, true, {\n                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/SportsGrid.tsx\",\n                                                lineNumber: 140,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/SportsGrid.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/SportsGrid.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, event.id, true, {\n                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/SportsGrid.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/SportsGrid.tsx\",\n                lineNumber: 73,\n                columnNumber: 7\n            }, this),\n            filteredEvents.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-gray-400 text-lg mb-2\",\n                        children: \"No events found\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/SportsGrid.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-gray-500 text-sm\",\n                        children: \"Try adjusting your filter or check back later\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/SportsGrid.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/SportsGrid.tsx\",\n                lineNumber: 186,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/SportsGrid.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/sports/SportsGrid.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/sports/TrendingBets.tsx":
/*!************************************************!*\
  !*** ./src/components/sports/TrendingBets.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TrendingBets: () => (/* binding */ TrendingBets)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/../../node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_FireIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,FireIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_FireIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,FireIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/FireIcon.js\");\n/* harmony import */ var _data_mock_data__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/data/mock-data */ \"(ssr)/./src/data/mock-data.ts\");\n/* __next_internal_client_entry_do_not_use__ TrendingBets auto */ \n\n\n\nfunction TrendingBets() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-dark-800 rounded-xl border border-dark-700 p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_FireIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        className: \"h-5 w-5 text-primary-400\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/TrendingBets.tsx\",\n                        lineNumber: 11,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-white\",\n                        children: \"Trending Bets\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/TrendingBets.tsx\",\n                        lineNumber: 12,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_FireIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        className: \"h-4 w-4 text-orange-400\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/TrendingBets.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/TrendingBets.tsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: _data_mock_data__WEBPACK_IMPORTED_MODULE_1__.mockTrendingBets.map((bet, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            x: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            x: 0\n                        },\n                        transition: {\n                            duration: 0.4,\n                            delay: index * 0.1\n                        },\n                        className: \"bg-dark-700 rounded-lg p-4 border border-dark-600 hover:border-primary-500/50 transition-colors cursor-pointer group\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start justify-between mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-white font-medium text-sm group-hover:text-primary-400 transition-colors\",\n                                                children: bet.event\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/TrendingBets.tsx\",\n                                                lineNumber: 27,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 text-xs mt-1\",\n                                                children: bet.selection\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/TrendingBets.tsx\",\n                                                lineNumber: 30,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/TrendingBets.tsx\",\n                                        lineNumber: 26,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-primary-400 font-bold\",\n                                        children: bet.odds.toFixed(2)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/TrendingBets.tsx\",\n                                        lineNumber: 34,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/TrendingBets.tsx\",\n                                lineNumber: 25,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full bg-dark-600 rounded-full h-2 flex-1 max-w-[100px]\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                initial: {\n                                                    width: 0\n                                                },\n                                                animate: {\n                                                    width: `${bet.percentage}%`\n                                                },\n                                                transition: {\n                                                    duration: 1,\n                                                    delay: 0.5 + index * 0.1\n                                                },\n                                                className: \"bg-gradient-to-r from-primary-500 to-secondary-500 h-2 rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/TrendingBets.tsx\",\n                                                lineNumber: 42,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/TrendingBets.tsx\",\n                                            lineNumber: 41,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-400 text-xs\",\n                                            children: [\n                                                bet.percentage,\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/TrendingBets.tsx\",\n                                            lineNumber: 49,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/TrendingBets.tsx\",\n                                    lineNumber: 40,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/TrendingBets.tsx\",\n                                lineNumber: 39,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, index, true, {\n                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/TrendingBets.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/TrendingBets.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n                whileHover: {\n                    scale: 1.02\n                },\n                whileTap: {\n                    scale: 0.98\n                },\n                className: \"w-full mt-4 bg-primary-600 hover:bg-primary-700 text-white py-2 rounded-lg font-medium transition-colors\",\n                children: \"View All Trends\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/TrendingBets.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/sports/TrendingBets.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/sports/TrendingBets.tsx\n");

/***/ }),

/***/ "(ssr)/./src/data/mock-data.ts":
/*!*******************************!*\
  !*** ./src/data/mock-data.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mockCasinoGames: () => (/* binding */ mockCasinoGames),\n/* harmony export */   mockEvents: () => (/* binding */ mockEvents),\n/* harmony export */   mockPlatformStats: () => (/* binding */ mockPlatformStats),\n/* harmony export */   mockRecentWinners: () => (/* binding */ mockRecentWinners),\n/* harmony export */   mockSports: () => (/* binding */ mockSports),\n/* harmony export */   mockTrendingBets: () => (/* binding */ mockTrendingBets),\n/* harmony export */   mockUserStats: () => (/* binding */ mockUserStats)\n/* harmony export */ });\n// Mock Sports Data\nconst mockSports = [\n    {\n        id: \"1\",\n        name: \"Football\",\n        slug: \"football\",\n        icon: \"⚽\",\n        isActive: true,\n        eventCount: 156\n    },\n    {\n        id: \"2\",\n        name: \"Basketball\",\n        slug: \"basketball\",\n        icon: \"\\uD83C\\uDFC0\",\n        isActive: true,\n        eventCount: 89\n    },\n    {\n        id: \"3\",\n        name: \"Tennis\",\n        slug: \"tennis\",\n        icon: \"\\uD83C\\uDFBE\",\n        isActive: true,\n        eventCount: 234\n    },\n    {\n        id: \"4\",\n        name: \"Cricket\",\n        slug: \"cricket\",\n        icon: \"\\uD83C\\uDFCF\",\n        isActive: true,\n        eventCount: 45\n    },\n    {\n        id: \"5\",\n        name: \"Ice Hockey\",\n        slug: \"ice-hockey\",\n        icon: \"\\uD83C\\uDFD2\",\n        isActive: true,\n        eventCount: 67\n    },\n    {\n        id: \"6\",\n        name: \"Baseball\",\n        slug: \"baseball\",\n        icon: \"⚾\",\n        isActive: true,\n        eventCount: 123\n    }\n];\n// Mock Events Data\nconst mockEvents = [\n    {\n        id: \"1\",\n        leagueId: \"1\",\n        homeTeam: {\n            id: \"1\",\n            name: \"Manchester United\",\n            logo: \"/teams/man-utd.png\"\n        },\n        awayTeam: {\n            id: \"2\",\n            name: \"Liverpool\",\n            logo: \"/teams/liverpool.png\"\n        },\n        startTime: \"2024-01-15T20:00:00.000Z\",\n        status: \"scheduled\",\n        isLive: false,\n        markets: [\n            {\n                id: \"1\",\n                eventId: \"1\",\n                name: \"Match Winner\",\n                type: \"match_winner\",\n                isActive: true,\n                isSuspended: false,\n                outcomes: [\n                    {\n                        id: \"1\",\n                        marketId: \"1\",\n                        name: \"Manchester United\",\n                        odds: 2.45,\n                        isActive: true,\n                        trend: \"up\"\n                    },\n                    {\n                        id: \"2\",\n                        marketId: \"1\",\n                        name: \"Draw\",\n                        odds: 3.20,\n                        isActive: true,\n                        trend: \"neutral\"\n                    },\n                    {\n                        id: \"3\",\n                        marketId: \"1\",\n                        name: \"Liverpool\",\n                        odds: 2.80,\n                        isActive: true,\n                        trend: \"down\"\n                    }\n                ]\n            },\n            {\n                id: \"2\",\n                eventId: \"1\",\n                name: \"Total Goals\",\n                type: \"over_under\",\n                isActive: true,\n                isSuspended: false,\n                outcomes: [\n                    {\n                        id: \"4\",\n                        marketId: \"2\",\n                        name: \"Over 2.5\",\n                        odds: 1.85,\n                        isActive: true,\n                        trend: \"up\"\n                    },\n                    {\n                        id: \"5\",\n                        marketId: \"2\",\n                        name: \"Under 2.5\",\n                        odds: 1.95,\n                        isActive: true,\n                        trend: \"down\"\n                    }\n                ]\n            }\n        ]\n    },\n    {\n        id: \"2\",\n        leagueId: \"1\",\n        homeTeam: {\n            id: \"3\",\n            name: \"Chelsea\",\n            logo: \"/teams/chelsea.png\"\n        },\n        awayTeam: {\n            id: \"4\",\n            name: \"Arsenal\",\n            logo: \"/teams/arsenal.png\"\n        },\n        startTime: \"2024-01-15T18:00:00.000Z\",\n        status: \"live\",\n        isLive: true,\n        homeScore: 1,\n        awayScore: 2,\n        minute: 67,\n        period: \"2nd Half\",\n        markets: [\n            {\n                id: \"3\",\n                eventId: \"2\",\n                name: \"Match Winner\",\n                type: \"match_winner\",\n                isActive: true,\n                isSuspended: false,\n                outcomes: [\n                    {\n                        id: \"6\",\n                        marketId: \"3\",\n                        name: \"Chelsea\",\n                        odds: 4.50,\n                        isActive: true,\n                        trend: \"up\"\n                    },\n                    {\n                        id: \"7\",\n                        marketId: \"3\",\n                        name: \"Draw\",\n                        odds: 3.80,\n                        isActive: true,\n                        trend: \"neutral\"\n                    },\n                    {\n                        id: \"8\",\n                        marketId: \"3\",\n                        name: \"Arsenal\",\n                        odds: 1.65,\n                        isActive: true,\n                        trend: \"down\"\n                    }\n                ]\n            }\n        ]\n    },\n    {\n        id: \"3\",\n        leagueId: \"2\",\n        homeTeam: {\n            id: \"5\",\n            name: \"Lakers\",\n            logo: \"/teams/lakers.png\"\n        },\n        awayTeam: {\n            id: \"6\",\n            name: \"Warriors\",\n            logo: \"/teams/warriors.png\"\n        },\n        startTime: \"2024-01-15T22:00:00.000Z\",\n        status: \"scheduled\",\n        isLive: false,\n        markets: [\n            {\n                id: \"4\",\n                eventId: \"3\",\n                name: \"Match Winner\",\n                type: \"match_winner\",\n                isActive: true,\n                isSuspended: false,\n                outcomes: [\n                    {\n                        id: \"9\",\n                        marketId: \"4\",\n                        name: \"Lakers\",\n                        odds: 1.95,\n                        isActive: true,\n                        trend: \"neutral\"\n                    },\n                    {\n                        id: \"10\",\n                        marketId: \"4\",\n                        name: \"Warriors\",\n                        odds: 1.85,\n                        isActive: true,\n                        trend: \"up\"\n                    }\n                ]\n            }\n        ]\n    }\n];\n// Mock Casino Games\nconst mockCasinoGames = [\n    {\n        id: \"1\",\n        name: \"Mega Moolah\",\n        provider: \"Microgaming\",\n        category: \"Slots\",\n        thumbnail: \"/games/mega-moolah.jpg\",\n        isLive: false,\n        minBet: 0.25,\n        maxBet: 6.25,\n        rtp: 88.12,\n        popularity: 95,\n        isNew: false,\n        isFeatured: true\n    },\n    {\n        id: \"2\",\n        name: \"Live Blackjack\",\n        provider: \"Evolution Gaming\",\n        category: \"Live Casino\",\n        thumbnail: \"/games/live-blackjack.jpg\",\n        isLive: true,\n        minBet: 5,\n        maxBet: 5000,\n        rtp: 99.28,\n        popularity: 88,\n        isNew: false,\n        isFeatured: true\n    },\n    {\n        id: \"3\",\n        name: \"Starburst\",\n        provider: \"NetEnt\",\n        category: \"Slots\",\n        thumbnail: \"/games/starburst.jpg\",\n        isLive: false,\n        minBet: 0.10,\n        maxBet: 100,\n        rtp: 96.09,\n        popularity: 92,\n        isNew: false,\n        isFeatured: false\n    },\n    {\n        id: \"4\",\n        name: \"Lightning Roulette\",\n        provider: \"Evolution Gaming\",\n        category: \"Live Casino\",\n        thumbnail: \"/games/lightning-roulette.jpg\",\n        isLive: true,\n        minBet: 0.20,\n        maxBet: 20000,\n        rtp: 97.30,\n        popularity: 90,\n        isNew: true,\n        isFeatured: true\n    }\n];\n// Mock User Stats\nconst mockUserStats = {\n    totalBets: 1247,\n    totalWins: 623,\n    totalLosses: 624,\n    winRate: 49.96,\n    totalStaked: 12450.00,\n    totalWon: 13890.50,\n    netProfit: 1440.50,\n    biggestWin: 2850.00,\n    currentStreak: 3,\n    longestStreak: 12\n};\n// Mock Platform Stats\nconst mockPlatformStats = {\n    totalUsers: 125847,\n    totalBets: 2847593,\n    totalVolume: 45892347.50,\n    liveEvents: 156,\n    popularSports: mockSports.slice(0, 3),\n    biggestWins: [\n        {\n            id: \"1\",\n            userId: \"user1\",\n            type: \"combo\",\n            selections: [],\n            stake: 50,\n            potentialWin: 15750,\n            totalOdds: 315,\n            status: \"won\",\n            placedAt: \"2024-01-15T16:00:00.000Z\",\n            settledAt: \"2024-01-15T17:00:00.000Z\"\n        },\n        {\n            id: \"2\",\n            userId: \"user2\",\n            type: \"single\",\n            selections: [],\n            stake: 1000,\n            potentialWin: 8500,\n            totalOdds: 8.5,\n            status: \"won\",\n            placedAt: \"2024-01-15T12:00:00.000Z\",\n            settledAt: \"2024-01-15T13:00:00.000Z\"\n        }\n    ]\n};\n// Mock trending bets\nconst mockTrendingBets = [\n    {\n        event: \"Manchester United vs Liverpool\",\n        selection: \"Over 2.5 Goals\",\n        odds: 1.85,\n        percentage: 78\n    },\n    {\n        event: \"Lakers vs Warriors\",\n        selection: \"Lakers to Win\",\n        odds: 1.95,\n        percentage: 65\n    },\n    {\n        event: \"Chelsea vs Arsenal\",\n        selection: \"Arsenal to Win\",\n        odds: 1.65,\n        percentage: 82\n    }\n];\n// Mock recent winners\nconst mockRecentWinners = [\n    {\n        username: \"BetMaster2023\",\n        amount: 15750,\n        game: \"Football Combo\",\n        time: \"2 minutes ago\"\n    },\n    {\n        username: \"LuckyPlayer\",\n        amount: 8500,\n        game: \"Live Blackjack\",\n        time: \"5 minutes ago\"\n    },\n    {\n        username: \"SportsFan\",\n        amount: 3200,\n        game: \"Basketball\",\n        time: \"8 minutes ago\"\n    }\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/data/mock-data.ts\n");

/***/ }),

/***/ "(ssr)/./src/store/betting-store.tsx":
/*!*************************************!*\
  !*** ./src/store/betting-store.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BettingProvider: () => (/* binding */ BettingProvider),\n/* harmony export */   useBetting: () => (/* binding */ useBetting)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ BettingProvider,useBetting auto */ \n\n// Initial state\nconst initialState = {\n    isOpen: false,\n    selections: [],\n    stake: 10,\n    betType: \"single\",\n    totalOdds: 1,\n    potentialWin: 0\n};\n// Utility functions\nconst calculateTotalOdds = (selections, betType)=>{\n    if (selections.length === 0) return 1;\n    if (betType === \"single\") {\n        return selections[0]?.odds || 1;\n    }\n    if (betType === \"combo\") {\n        return selections.reduce((total, selection)=>total * selection.odds, 1);\n    }\n    // System bets would have more complex calculations\n    return selections.reduce((total, selection)=>total * selection.odds, 1);\n};\nconst calculatePotentialWin = (stake, totalOdds)=>{\n    return stake * totalOdds;\n};\n// Reducer\nfunction bettingReducer(state, action) {\n    switch(action.type){\n        case \"ADD_SELECTION\":\n            {\n                // Check if selection already exists\n                const existingIndex = state.selections.findIndex((s)=>s.outcomeId === action.payload.outcomeId);\n                let newSelections;\n                if (existingIndex >= 0) {\n                    // Update existing selection\n                    newSelections = state.selections.map((selection, index)=>index === existingIndex ? action.payload : selection);\n                } else {\n                    // Add new selection\n                    newSelections = [\n                        ...state.selections,\n                        action.payload\n                    ];\n                }\n                const totalOdds = calculateTotalOdds(newSelections, state.betType);\n                const potentialWin = calculatePotentialWin(state.stake, totalOdds);\n                return {\n                    ...state,\n                    selections: newSelections,\n                    totalOdds,\n                    potentialWin,\n                    isOpen: true\n                };\n            }\n        case \"REMOVE_SELECTION\":\n            {\n                const newSelections = state.selections.filter((s)=>s.outcomeId !== action.payload);\n                const totalOdds = calculateTotalOdds(newSelections, state.betType);\n                const potentialWin = calculatePotentialWin(state.stake, totalOdds);\n                return {\n                    ...state,\n                    selections: newSelections,\n                    totalOdds,\n                    potentialWin,\n                    isOpen: newSelections.length > 0 ? state.isOpen : false\n                };\n            }\n        case \"CLEAR_SELECTIONS\":\n            return {\n                ...state,\n                selections: [],\n                totalOdds: 1,\n                potentialWin: 0\n            };\n        case \"SET_STAKE\":\n            {\n                const potentialWin = calculatePotentialWin(action.payload, state.totalOdds);\n                return {\n                    ...state,\n                    stake: action.payload,\n                    potentialWin\n                };\n            }\n        case \"SET_BET_TYPE\":\n            {\n                const totalOdds = calculateTotalOdds(state.selections, action.payload);\n                const potentialWin = calculatePotentialWin(state.stake, totalOdds);\n                return {\n                    ...state,\n                    betType: action.payload,\n                    totalOdds,\n                    potentialWin\n                };\n            }\n        case \"TOGGLE_SLIP\":\n            return {\n                ...state,\n                isOpen: !state.isOpen\n            };\n        case \"OPEN_SLIP\":\n            return {\n                ...state,\n                isOpen: true\n            };\n        case \"CLOSE_SLIP\":\n            return {\n                ...state,\n                isOpen: false\n            };\n        case \"UPDATE_ODDS\":\n            {\n                const newSelections = state.selections.map((selection)=>selection.outcomeId === action.payload.outcomeId ? {\n                        ...selection,\n                        odds: action.payload.odds\n                    } : selection);\n                const totalOdds = calculateTotalOdds(newSelections, state.betType);\n                const potentialWin = calculatePotentialWin(state.stake, totalOdds);\n                return {\n                    ...state,\n                    selections: newSelections,\n                    totalOdds,\n                    potentialWin\n                };\n            }\n        default:\n            return state;\n    }\n}\n// Context\nconst BettingContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(null);\n// Provider\nfunction BettingProvider({ children }) {\n    const [state, dispatch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useReducer)(bettingReducer, initialState);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BettingContext.Provider, {\n        value: {\n            state,\n            dispatch\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/store/betting-store.tsx\",\n        lineNumber: 179,\n        columnNumber: 5\n    }, this);\n}\n// Hook\nfunction useBetting() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(BettingContext);\n    if (!context) {\n        throw new Error(\"useBetting must be used within a BettingProvider\");\n    }\n    const { state, dispatch } = context;\n    return {\n        // State\n        ...state,\n        // Actions\n        addSelection: (selection)=>dispatch({\n                type: \"ADD_SELECTION\",\n                payload: selection\n            }),\n        removeSelection: (outcomeId)=>dispatch({\n                type: \"REMOVE_SELECTION\",\n                payload: outcomeId\n            }),\n        clearSelections: ()=>dispatch({\n                type: \"CLEAR_SELECTIONS\"\n            }),\n        setStake: (stake)=>dispatch({\n                type: \"SET_STAKE\",\n                payload: stake\n            }),\n        setBetType: (betType)=>dispatch({\n                type: \"SET_BET_TYPE\",\n                payload: betType\n            }),\n        toggleSlip: ()=>dispatch({\n                type: \"TOGGLE_SLIP\"\n            }),\n        openSlip: ()=>dispatch({\n                type: \"OPEN_SLIP\"\n            }),\n        closeSlip: ()=>dispatch({\n                type: \"CLOSE_SLIP\"\n            }),\n        updateOdds: (outcomeId, odds)=>dispatch({\n                type: \"UPDATE_ODDS\",\n                payload: {\n                    outcomeId,\n                    odds\n                }\n            }),\n        // Computed values\n        hasSelections: state.selections.length > 0,\n        selectionCount: state.selections.length,\n        canPlaceBet: state.selections.length > 0 && state.stake > 0\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/store/betting-store.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"eac7beb055b5\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQGtlc2FyLW1hbmdvL3dlYi8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/MDlhMCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImVhYzdiZWIwNTViNVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/../../node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./providers */ \"(rsc)/./src/app/providers.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(rsc)/../../node_modules/react-hot-toast/dist/index.mjs\");\n\n\n\n\n\nconst metadata = {\n    title: \"Kesar Mango - Premium Sports & Casino Betting\",\n    description: \"Experience the ultimate betting platform with live sports betting, casino games, and real-time odds. Join thousands of winners today!\",\n    keywords: \"sports betting, casino games, live betting, online gambling, odds, poker, slots\",\n    authors: [\n        {\n            name: \"Kesar Mango Team\"\n        }\n    ],\n    creator: \"Kesar Mango\",\n    publisher: \"Kesar Mango\",\n    formatDetection: {\n        email: false,\n        address: false,\n        telephone: false\n    },\n    metadataBase: new URL(\"https://kesarmango.com\"),\n    alternates: {\n        canonical: \"/\"\n    },\n    openGraph: {\n        title: \"Kesar Mango - Premium Sports & Casino Betting\",\n        description: \"Experience the ultimate betting platform with live sports betting, casino games, and real-time odds.\",\n        url: \"https://kesarmango.com\",\n        siteName: \"Kesar Mango\",\n        images: [\n            {\n                url: \"/og-image.jpg\",\n                width: 1200,\n                height: 630,\n                alt: \"Kesar Mango Betting Platform\"\n            }\n        ],\n        locale: \"en_US\",\n        type: \"website\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"Kesar Mango - Premium Sports & Casino Betting\",\n        description: \"Experience the ultimate betting platform with live sports betting, casino games, and real-time odds.\",\n        images: [\n            \"/og-image.jpg\"\n        ],\n        creator: \"@kesarmango\"\n    },\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            \"max-video-preview\": -1,\n            \"max-image-preview\": \"large\",\n            \"max-snippet\": -1\n        }\n    },\n    verification: {\n        google: \"your-google-verification-code\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"dark\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().className)} bg-dark-950 text-white min-h-screen`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers__WEBPACK_IMPORTED_MODULE_2__.Providers, {\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.Toaster, {\n                        position: \"top-right\",\n                        toastOptions: {\n                            duration: 4000,\n                            style: {\n                                background: \"#1e293b\",\n                                color: \"#fff\",\n                                border: \"1px solid #475569\"\n                            },\n                            success: {\n                                iconTheme: {\n                                    primary: \"#22c55e\",\n                                    secondary: \"#fff\"\n                                }\n                            },\n                            error: {\n                                iconTheme: {\n                                    primary: \"#ef4444\",\n                                    secondary: \"#fff\"\n                                }\n                            }\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/app/layout.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/app/layout.tsx\",\n                lineNumber: 72,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/app/layout.tsx\",\n            lineNumber: 71,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/app/layout.tsx\",\n        lineNumber: 70,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/kesar_mango/apps/web/src/app/page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/providers.tsx":
/*!*******************************!*\
  !*** ./src/app/providers.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/kesar_mango/apps/web/src/app/providers.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/kesar_mango/apps/web/src/app/providers.tsx#Providers`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/framer-motion","vendor-chunks/@heroicons","vendor-chunks/@swc","vendor-chunks/react-hot-toast","vendor-chunks/goober"], () => (__webpack_exec__("(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fmantrarajgotecha%2Fkesar_mango%2Fapps%2Fweb%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmantrarajgotecha%2Fkesar_mango%2Fapps%2Fweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();