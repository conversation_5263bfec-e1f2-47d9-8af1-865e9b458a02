/** @type {import('next').NextConfig} */
const nextConfig = {
  
  // Optimize for betting platform performance
  compress: true,
  poweredByHeader: false,
  
  // Security headers for betting platform
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin',
          },
          {
            key: 'Content-Security-Policy',
            value: "default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' wss: https:;",
          },
        ],
      },
    ];
  },
  
  // Optimize images for betting content
  images: {
    domains: ['images.unsplash.com', 'cdn.betting-platform.com', 'flagcdn.com'],
    formats: ['image/webp', 'image/avif'],
  },
  
  // Enable SWC minification for better performance
  swcMinify: true,
  
  // Optimize for production
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production',
  },
};

module.exports = nextConfig;
